/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
  A class for the Servicemanager ASN.1/Java  project.
*/
public class Servicemanager extends ASN1Project {

    /**
     * Initialize the pdu decoder.
     */
    private static final ProjectInfo c_projectInfo = new ProjectInfo (
	null,
	new byte[] {
	    (byte)0x0b, (byte)0x27, (byte)0xb4, (byte)0xa6, (byte)0x8b,
	    (byte)0x46, (byte)0x3f, (byte)0x73, (byte)0x10, (byte)0xc6,
	    (byte)0xd3, (byte)0xe0, (byte)0x78, (byte)0x8a, (byte)0xaa,
	    (byte)0xbe, (byte)0xc3, (byte)0x74, (byte)0xd5, (byte)0xb8,
	    (byte)0x41, (byte)0x12, (byte)0xb1, (byte)0x86, (byte)0x1c,
	    (byte)0x2a, (byte)0x64, (byte)0x28, (byte)0x37, (byte)0xf2,
	    (byte)0x5c, (byte)0x05, (byte)0x1a, (byte)0xae, (byte)0xea,
	    (byte)0x95, (byte)0x54, (byte)0xc8, (byte)0x97, (byte)0xba,
	    (byte)0xb2, (byte)0x58, (byte)0xe3, (byte)0x18, (byte)0xb7,
	    (byte)0xc4, (byte)0xf7, (byte)0x42, (byte)0x1c, (byte)0x86,
	    (byte)0xb1, (byte)0x12, (byte)0x49, (byte)0xd9, (byte)0x70,
	    (byte)0xa1, (byte)0x66, (byte)0x35, (byte)0x0f, (byte)0x11,
	    (byte)0x16, (byte)0xfa, (byte)0x61, (byte)0x58, (byte)0x39,
	    (byte)0x03, (byte)0x09, (byte)0x4f
	},
	"2025/08/06"
    );
    
    /**
     * Get the project descriptor of 'this' object.
     */
    public ProjectInfo getProjectInfo()
    {
	return c_projectInfo;
    }
    
    private static final ASN1Project c_project = new Servicemanager();

    /**
     * Methods for accessing Coders.
     */
    public static Coder getDefaultCoder()
    {
	return createCOERCoder(c_project);
    }
    
    public static OERCoder getOERCoder()
    {
	return createOERCoder(c_project);
    }
    
    public static COERCoder getCOERCoder()
    {
	return createCOERCoder(c_project);
    }
    
}
