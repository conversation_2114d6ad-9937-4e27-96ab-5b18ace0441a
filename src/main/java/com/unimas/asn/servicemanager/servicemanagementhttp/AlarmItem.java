/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the AlarmItem ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class AlarmItem extends Sequence {
    
    /**
     * The default constructor.
     */
    public AlarmItem()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public AlarmItem(AlarmType alarmType, AlarmCode alarmCode, 
		    IA5String alarmDesc, Uint64 alarmCount)
    {
	setAlarmType(alarmType);
	setAlarmCode(alarmCode);
	setAlarmDesc(alarmDesc);
	setAlarmCount(alarmCount);
    }
    
    /**
     * Construct with required components.
     */
    public AlarmItem(AlarmType alarmType, AlarmCode alarmCode, 
		    Uint64 alarmCount)
    {
	setAlarmType(alarmType);
	setAlarmCode(alarmCode);
	setAlarmCount(alarmCount);
    }
    
    public void initComponents()
    {
	mComponents[0] = AlarmType.faultAlarm;
	mComponents[1] = AlarmCode.illegalCertificate;
	mComponents[2] = new IA5String();
	mComponents[3] = new Uint64();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[4];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return AlarmType.faultAlarm;
	    case 1:
		return AlarmCode.illegalCertificate;
	    case 2:
		return new IA5String();
	    case 3:
		return new Uint64();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "alarmType"
    public AlarmType getAlarmType()
    {
	return (AlarmType)mComponents[0];
    }
    
    public void setAlarmType(AlarmType alarmType)
    {
	mComponents[0] = alarmType;
    }
    
    
    // Methods for field "alarmCode"
    public AlarmCode getAlarmCode()
    {
	return (AlarmCode)mComponents[1];
    }
    
    public void setAlarmCode(AlarmCode alarmCode)
    {
	mComponents[1] = alarmCode;
    }
    
    
    // Methods for field "alarmDesc"
    public IA5String getAlarmDesc()
    {
	return (IA5String)mComponents[2];
    }
    
    public void setAlarmDesc(IA5String alarmDesc)
    {
	mComponents[2] = alarmDesc;
    }
    
    public boolean hasAlarmDesc()
    {
	return componentIsPresent(2);
    }
    
    public void deleteAlarmDesc()
    {
	setComponentAbsent(2);
    }
    
    
    // Methods for field "alarmCount"
    public Uint64 getAlarmCount()
    {
	return (Uint64)mComponents[3];
    }
    
    public void setAlarmCount(Uint64 alarmCount)
    {
	mComponents[3] = alarmCount;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "AlarmItem"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "AlarmItem"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AlarmType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AlarmType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"faultAlarm",
					1
				    ),
				    new MemberListElement (
					"securityAlarm",
					2
				    )
				}
			    ),
			    0,
			    AlarmType.faultAlarm
			)
		    ),
		    "alarmType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"AlarmCode"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"AlarmCode"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"illegalCertificate",
					1
				    ),
				    new MemberListElement (
					"deviceException",
					2
				    ),
				    new MemberListElement (
					"channelException",
					3
				    ),
				    new MemberListElement (
					"protocolVerifyFailure",
					4
				    ),
				    new MemberListElement (
					"keywordCheckFailure",
					5
				    )
				}
			    ),
			    0,
			    AlarmCode.illegalCertificate
			)
		    ),
		    "alarmCode",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new KMCStringInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"IA5String"
			    ),
			    new QName (
				"builtin",
				"IA5String"
			    ),
			    536603,
			    new Intersection (
				new SizeConstraint (
				    new ValueRangeConstraint (
					new AbstractBounds(
					    new com.oss.asn1.INTEGER(0), 
					    new com.oss.asn1.INTEGER(200),
					    0
					)
				    )
				),
				new PermittedAlphabetConstraint (
				    IA5StringPAInfo.pa
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(200)
			    ),
			    null
			)
		    ),
		    "alarmDesc",
		    2,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "alarmCount",
		    3,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2),
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' AlarmItem object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' AlarmItem object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for AlarmItem
