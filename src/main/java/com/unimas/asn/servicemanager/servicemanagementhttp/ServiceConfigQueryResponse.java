/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the ServiceConfigQueryResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class ServiceConfigQueryResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public ServiceConfigQueryResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public ServiceConfigQueryResponse(ContentMessageType messageType, 
		    ServiceId serviceId, DisplayName displayname, 
		    Network network, IPAddress proxyIp, PortNumber proxyPort, 
		    IPAddress serverIp, PortNumber serverPort, 
		    ContentKeyCheck contentKeyCheck, 
		    ProtocolFilter protocolFilter, PermissionState unpassDeal)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setDisplayname(displayname);
	setNetwork(network);
	setProxyIp(proxyIp);
	setProxyPort(proxyPort);
	setServerIp(serverIp);
	setServerPort(serverPort);
	setContentKeyCheck(contentKeyCheck);
	setProtocolFilter(protocolFilter);
	setUnpassDeal(unpassDeal);
    }
    
    /**
     * Construct with required components.
     */
    public ServiceConfigQueryResponse(ContentMessageType messageType, 
		    ServiceId serviceId, DisplayName displayname, 
		    Network network)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setDisplayname(displayname);
	setNetwork(network);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new ServiceId();
	mComponents[2] = new DisplayName();
	mComponents[3] = Network.sender;
	mComponents[4] = new IPAddress();
	mComponents[5] = new PortNumber();
	mComponents[6] = new IPAddress();
	mComponents[7] = new PortNumber();
	mComponents[8] = new ContentKeyCheck();
	mComponents[9] = new ProtocolFilter();
	mComponents[10] = PermissionState.allow;
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[11];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new ServiceId();
	    case 2:
		return new DisplayName();
	    case 3:
		return Network.sender;
	    case 4:
		return new IPAddress();
	    case 5:
		return new PortNumber();
	    case 6:
		return new IPAddress();
	    case 7:
		return new PortNumber();
	    case 8:
		return new ContentKeyCheck();
	    case 9:
		return new ProtocolFilter();
	    case 10:
		return PermissionState.allow;
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceId"
    public ServiceId getServiceId()
    {
	return (ServiceId)mComponents[1];
    }
    
    public void setServiceId(ServiceId serviceId)
    {
	mComponents[1] = serviceId;
    }
    
    
    // Methods for field "displayname"
    public DisplayName getDisplayname()
    {
	return (DisplayName)mComponents[2];
    }
    
    public void setDisplayname(DisplayName displayname)
    {
	mComponents[2] = displayname;
    }
    
    
    // Methods for field "network"
    public Network getNetwork()
    {
	return (Network)mComponents[3];
    }
    
    public void setNetwork(Network network)
    {
	mComponents[3] = network;
    }
    
    
    // Methods for field "proxyIp"
    public IPAddress getProxyIp()
    {
	return (IPAddress)mComponents[4];
    }
    
    public void setProxyIp(IPAddress proxyIp)
    {
	mComponents[4] = proxyIp;
    }
    
    public boolean hasProxyIp()
    {
	return componentIsPresent(4);
    }
    
    public void deleteProxyIp()
    {
	setComponentAbsent(4);
    }
    
    
    // Methods for field "proxyPort"
    public PortNumber getProxyPort()
    {
	return (PortNumber)mComponents[5];
    }
    
    public void setProxyPort(PortNumber proxyPort)
    {
	mComponents[5] = proxyPort;
    }
    
    public boolean hasProxyPort()
    {
	return componentIsPresent(5);
    }
    
    public void deleteProxyPort()
    {
	setComponentAbsent(5);
    }
    
    
    // Methods for field "serverIp"
    public IPAddress getServerIp()
    {
	return (IPAddress)mComponents[6];
    }
    
    public void setServerIp(IPAddress serverIp)
    {
	mComponents[6] = serverIp;
    }
    
    public boolean hasServerIp()
    {
	return componentIsPresent(6);
    }
    
    public void deleteServerIp()
    {
	setComponentAbsent(6);
    }
    
    
    // Methods for field "serverPort"
    public PortNumber getServerPort()
    {
	return (PortNumber)mComponents[7];
    }
    
    public void setServerPort(PortNumber serverPort)
    {
	mComponents[7] = serverPort;
    }
    
    public boolean hasServerPort()
    {
	return componentIsPresent(7);
    }
    
    public void deleteServerPort()
    {
	setComponentAbsent(7);
    }
    
    
    // Methods for field "contentKeyCheck"
    public ContentKeyCheck getContentKeyCheck()
    {
	return (ContentKeyCheck)mComponents[8];
    }
    
    public void setContentKeyCheck(ContentKeyCheck contentKeyCheck)
    {
	mComponents[8] = contentKeyCheck;
    }
    
    public boolean hasContentKeyCheck()
    {
	return componentIsPresent(8);
    }
    
    public void deleteContentKeyCheck()
    {
	setComponentAbsent(8);
    }
    
    
    // Methods for field "protocolFilter"
    public ProtocolFilter getProtocolFilter()
    {
	return (ProtocolFilter)mComponents[9];
    }
    
    public void setProtocolFilter(ProtocolFilter protocolFilter)
    {
	mComponents[9] = protocolFilter;
    }
    
    public boolean hasProtocolFilter()
    {
	return componentIsPresent(9);
    }
    
    public void deleteProtocolFilter()
    {
	setComponentAbsent(9);
    }
    
    
    // Methods for field "unpassDeal"
    public PermissionState getUnpassDeal()
    {
	return (PermissionState)mComponents[10];
    }
    
    public void setUnpassDeal(PermissionState unpassDeal)
    {
	mComponents[10] = unpassDeal;
    }
    
    public boolean hasUnpassDeal()
    {
	return componentIsPresent(10);
    }
    
    public void deleteUnpassDeal()
    {
	setComponentAbsent(10);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "ServiceConfigQueryResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "ServiceConfigQueryResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryAlarm",
					8
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					9
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					10
				    ),
				    new MemberListElement (
					"sendPacketStats",
					11
				    ),
				    new MemberListElement (
					"receivePacketStats",
					12
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					13
				    ),
				    new MemberListElement (
					"hostMngService",
					14
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					15
				    ),
				    new MemberListElement (
					"iprouteService",
					16
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceId"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceId"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new ServiceId(0), 
				    new ServiceId(32767),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(32767)
			    ),
			    null,
			    2
			)
		    ),
		    "serviceId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new VectorInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"DisplayName"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"DisplayName"
			    ),
			    536603,
			    new SizeConstraint (
				new ValueRangeConstraint (
				    new AbstractBounds(
					new com.oss.asn1.INTEGER(1), 
					new com.oss.asn1.INTEGER(40),
					0
				    )
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1),
				java.lang.Long.valueOf(40)
			    )
			)
		    ),
		    "displayname",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Network"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Network"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"sender",
					0
				    ),
				    new MemberListElement (
					"receiver",
					1
				    )
				}
			    ),
			    0,
			    Network.sender
			)
		    ),
		    "network",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "proxyIp",
		    4,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "proxyPort",
		    5,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8006
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "serverIp",
		    6,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8007
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PortNumber"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PortNumber"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new PortNumber(1025), 
				    new PortNumber(65535),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(1025),
				java.lang.Long.valueOf(65535)
			    ),
			    null,
			    2
			)
		    ),
		    "serverPort",
		    7,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new BitStringInfo (
			    new Tags (
				new short[] {
				    (short)0x8008
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentKeyCheck"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentKeyCheck"
			    ),
			    536607,
			    new SizeConstraint (
				new ExtensibleConstraint (
				    new SingleValueConstraint (
					new com.oss.asn1.INTEGER(8)
				    ),
				    null
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(8),
				java.lang.Long.valueOf(8)
			    ),
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"carcheck",
					0
				    ),
				    new MemberListElement (
					"idcheck",
					1
				    )
				}
			    )
			)
		    ),
		    "contentKeyCheck",
		    8,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new BitStringInfo (
			    new Tags (
				new short[] {
				    (short)0x8009
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ProtocolFilter"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ProtocolFilter"
			    ),
			    536607,
			    new SizeConstraint (
				new ExtensibleConstraint (
				    new SingleValueConstraint (
					new com.oss.asn1.INTEGER(8)
				    ),
				    null
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(8),
				java.lang.Long.valueOf(8)
			    ),
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"crcfilter",
					0
				    ),
				    new MemberListElement (
					"asnfilter",
					1
				    )
				}
			    )
			)
		    ),
		    "protocolFilter",
		    9,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x800a
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PermissionState"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PermissionState"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"allow",
					0
				    ),
				    new MemberListElement (
					"forbidden",
					1
				    )
				}
			    ),
			    0,
			    PermissionState.allow
			)
		    ),
		    "unpassDeal",
		    10,
		    3,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4),
			new TagDecoderElement((short)0x8005, 5),
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5),
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8006, 6),
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8007, 7),
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8008, 8),
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8009, 9),
			new TagDecoderElement((short)0x800a, 10)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x800a, 10)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' ServiceConfigQueryResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' ServiceConfigQueryResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for ServiceConfigQueryResponse
