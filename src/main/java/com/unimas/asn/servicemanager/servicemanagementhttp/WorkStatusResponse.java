/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the WorkStatusResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class WorkStatusResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public WorkStatusResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public WorkStatusResponse(ContentMessageType messageType, 
		    ServiceId serviceId, Uint64 sendPkgNumToday, 
		    Uint64 sendPkgSizeToday, Uint64 recvPkgNumToday, 
		    Uint64 recvPkgSizeToday, Uint64 devTime)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setSendPkgNumToday(sendPkgNumToday);
	setSendPkgSizeToday(sendPkgSizeToday);
	setRecvPkgNumToday(recvPkgNumToday);
	setRecvPkgSizeToday(recvPkgSizeToday);
	setDevTime(devTime);
    }
    
    /**
     * Construct with required components.
     */
    public WorkStatusResponse(ContentMessageType messageType, 
		    ServiceId serviceId, Uint64 sendPkgNumToday, 
		    Uint64 sendPkgSizeToday, Uint64 recvPkgNumToday, 
		    Uint64 recvPkgSizeToday)
    {
	setMessageType(messageType);
	setServiceId(serviceId);
	setSendPkgNumToday(sendPkgNumToday);
	setSendPkgSizeToday(sendPkgSizeToday);
	setRecvPkgNumToday(recvPkgNumToday);
	setRecvPkgSizeToday(recvPkgSizeToday);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new ServiceId();
	mComponents[2] = new Uint64();
	mComponents[3] = new Uint64();
	mComponents[4] = new Uint64();
	mComponents[5] = new Uint64();
	mComponents[6] = new Uint64();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[7];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new ServiceId();
	    case 2:
		return new Uint64();
	    case 3:
		return new Uint64();
	    case 4:
		return new Uint64();
	    case 5:
		return new Uint64();
	    case 6:
		return new Uint64();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceId"
    public ServiceId getServiceId()
    {
	return (ServiceId)mComponents[1];
    }
    
    public void setServiceId(ServiceId serviceId)
    {
	mComponents[1] = serviceId;
    }
    
    
    // Methods for field "sendPkgNumToday"
    public Uint64 getSendPkgNumToday()
    {
	return (Uint64)mComponents[2];
    }
    
    public void setSendPkgNumToday(Uint64 sendPkgNumToday)
    {
	mComponents[2] = sendPkgNumToday;
    }
    
    
    // Methods for field "sendPkgSizeToday"
    public Uint64 getSendPkgSizeToday()
    {
	return (Uint64)mComponents[3];
    }
    
    public void setSendPkgSizeToday(Uint64 sendPkgSizeToday)
    {
	mComponents[3] = sendPkgSizeToday;
    }
    
    
    // Methods for field "recvPkgNumToday"
    public Uint64 getRecvPkgNumToday()
    {
	return (Uint64)mComponents[4];
    }
    
    public void setRecvPkgNumToday(Uint64 recvPkgNumToday)
    {
	mComponents[4] = recvPkgNumToday;
    }
    
    
    // Methods for field "recvPkgSizeToday"
    public Uint64 getRecvPkgSizeToday()
    {
	return (Uint64)mComponents[5];
    }
    
    public void setRecvPkgSizeToday(Uint64 recvPkgSizeToday)
    {
	mComponents[5] = recvPkgSizeToday;
    }
    
    
    // Methods for field "devTime"
    public Uint64 getDevTime()
    {
	return (Uint64)mComponents[6];
    }
    
    public void setDevTime(Uint64 devTime)
    {
	mComponents[6] = devTime;
    }
    
    public boolean hasDevTime()
    {
	return componentIsPresent(6);
    }
    
    public void deleteDevTime()
    {
	setComponentAbsent(6);
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "WorkStatusResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "WorkStatusResponse"
	),
	536607,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryAlarm",
					8
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					9
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					10
				    ),
				    new MemberListElement (
					"sendPacketStats",
					11
				    ),
				    new MemberListElement (
					"receivePacketStats",
					12
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					13
				    ),
				    new MemberListElement (
					"hostMngService",
					14
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					15
				    ),
				    new MemberListElement (
					"iprouteService",
					16
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ServiceId"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ServiceId"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new ServiceId(0), 
				    new ServiceId(32767),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(32767)
			    ),
			    null,
			    2
			)
		    ),
		    "serviceId",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "sendPkgNumToday",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "sendPkgSizeToday",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "recvPkgNumToday",
		    4,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "recvPkgSizeToday",
		    5,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new HugeIntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8006
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint64"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint64"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint64(new java.math.BigInteger("0")), 
				    new Uint64(new java.math.BigInteger("18446744073709551615")),
				    0
				)
			    ),
			    null,
			    null,
			    8,
			    null
			)
		    ),
		    "devTime",
		    6,
		    3,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8006, 6)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' WorkStatusResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' WorkStatusResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for WorkStatusResponse
