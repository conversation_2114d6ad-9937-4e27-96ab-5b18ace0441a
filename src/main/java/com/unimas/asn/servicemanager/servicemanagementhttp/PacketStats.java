/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the PacketStats ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class PacketStats extends Sequence {
    
    /**
     * The default constructor.
     */
    public PacketStats()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public PacketStats(PacketType packetType, Uint32 packetCount)
    {
	setPacketType(packetType);
	setPacketCount(packetCount);
    }
    
    public void initComponents()
    {
	mComponents[0] = PacketType.cim;
	mComponents[1] = new Uint32();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return PacketType.cim;
	    case 1:
		return new Uint32();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "packetType"
    public PacketType getPacketType()
    {
	return (PacketType)mComponents[0];
    }
    
    public void setPacketType(PacketType packetType)
    {
	mComponents[0] = packetType;
    }
    
    
    // Methods for field "packetCount"
    public Uint32 getPacketCount()
    {
	return (Uint32)mComponents[1];
    }
    
    public void setPacketCount(Uint32 packetCount)
    {
	mComponents[1] = packetCount;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "PacketStats"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "PacketStats"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"PacketType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"PacketType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"cim",
					1
				    ),
				    new MemberListElement (
					"slgs",
					2
				    ),
				    new MemberListElement (
					"lcs",
					3
				    ),
				    new MemberListElement (
					"bsm",
					4
				    ),
				    new MemberListElement (
					"other",
					9
				    )
				}
			    ),
			    0,
			    PacketType.cim
			)
		    ),
		    "packetType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"Uint32"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"Uint32"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new Uint32(0), 
				    new Uint32(4294967295L),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(4294967295L)
			    ),
			    null,
			    4
			)
		    ),
		    "packetCount",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for PacketStats
