/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the Double ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Real
 */

public class Double extends Real {
    
    /**
     * The default constructor.
     */
    public Double()
    {
    }
    
    /**
     * Construct from a float type.
     * @param value the float object to set this object to.
     */
    public Double(float value)
    {
	super(value);
    }
    
    /**
     * Construct from a double type.
     * @param value the double object to set this object to.
     */
    public Double(double value)
    {
	super(value);
    }
    
    /**
     * Initialize the type descriptor.
     */
    private static final RealInfo c_typeinfo = new RealInfo (
	new Tags (
	    new short[] {
		0x0009
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "Double"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "Double"
	),
	536603,
	null,
	8,
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.Double object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' com.unimas.asn.servicemanager.servicemanagementhttp.Double object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * The type is a PDU.
     */
    public boolean isPDU()
    {
	return true;
    }
    
} // End class definition for Double
