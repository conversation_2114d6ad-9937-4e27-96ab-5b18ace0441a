/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the GetAllServiceIdsResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class GetAllServiceIdsResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public GetAllServiceIdsResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public GetAllServiceIdsResponse(ContentMessageType messageType, 
		    ServiceIds serviceIds)
    {
	setMessageType(messageType);
	setServiceIds(serviceIds);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = new ServiceIds();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[2];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return new ServiceIds();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "serviceIds"
    public ServiceIds getServiceIds()
    {
	return (ServiceIds)mComponents[1];
    }
    
    public void setServiceIds(ServiceIds serviceIds)
    {
	mComponents[1] = serviceIds;
    }
    
    
    
    /**
     * Define the ServiceIds ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
     * @see SequenceOf
     */
    public static class ServiceIds extends SequenceOf<ServiceId> {
	
	/**
	 * The default constructor.
	 */
	public ServiceIds()
	{
	}
	
	/**
	 * Construct from an array of components.
	 */
	public ServiceIds(ServiceId[] elements)
	{
	    super(elements);
	}
	
	/**
	 * Create an instance of  SEQUENCE OF/SET OF.
	 */
	public AbstractData createInstance()
	{
	    return (AbstractData)new ServiceId();
	}
	
	/**
	 * Initialize the type descriptor.
	 */
	private static final ContainerInfo c_typeinfo = new ContainerInfo (
	    new Tags (
		new short[] {
		    (short)0x8001
		}
	    ),
	    new QName (
		"com.unimas.asn.servicemanager.servicemanagementhttp",
		"GetAllServiceIdsResponse$ServiceIds"
	    ),
	    new QName (
		"builtin",
		"SEQUENCE OF"
	    ),
	    536603,
	    null,
	    null,
	    new TypeInfoRef (
		new QName (
		    "com.unimas.asn.servicemanager.servicemanagementhttp",
		    "ServiceId"
		)
	    )
	);
	
	/**
	 * Get the type descriptor (TypeInfo) of 'this' ServiceIds object.
	 */
	public TypeInfo getTypeInfo()
	{
	    return c_typeinfo;
	}
	
	/**
	 * Get the static type descriptor (TypeInfo) of 'this' ServiceIds object.
	 */
	public static TypeInfo getStaticTypeInfo()
	{
	    return c_typeinfo;
	}
	
    } // End class definition for ServiceIds

    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "GetAllServiceIdsResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "GetAllServiceIdsResponse"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryAlarm",
					8
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					9
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					10
				    ),
				    new MemberListElement (
					"sendPacketStats",
					11
				    ),
				    new MemberListElement (
					"receivePacketStats",
					12
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					13
				    ),
				    new MemberListElement (
					"hostMngService",
					14
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					15
				    ),
				    new MemberListElement (
					"iprouteService",
					16
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new QName (
			    "com.unimas.asn.servicemanager.servicemanagementhttp",
			    "GetAllServiceIdsResponse$ServiceIds"
			)
		    ),
		    "serviceIds",
		    1,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' GetAllServiceIdsResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' GetAllServiceIdsResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for GetAllServiceIdsResponse
