/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: tb (Trial), License 89081Z 89081Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Wed Jul  2 16:57:40 2025 */
/* ASN.1 Compiler for Java version: 8.8 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the SetInterfaceIpResponse ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class SetInterfaceIpResponse extends Sequence {
    
    /**
     * The default constructor.
     */
    public SetInterfaceIpResponse()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public SetInterfaceIpResponse(ContentMessageType messageType, 
		    InterfaceType interfaceType, IPAddress currentIpAddress, 
		    IPAddress currentSubnetMask, IPAddress currentGateway, 
		    INTEGER result)
    {
	setMessageType(messageType);
	setInterfaceType(interfaceType);
	setCurrentIpAddress(currentIpAddress);
	setCurrentSubnetMask(currentSubnetMask);
	setCurrentGateway(currentGateway);
	setResult(result);
    }
    
    /**
     * Construct with components.
     */
    public SetInterfaceIpResponse(ContentMessageType messageType, 
		    InterfaceType interfaceType, IPAddress currentIpAddress, 
		    IPAddress currentSubnetMask, IPAddress currentGateway, 
		    long result)
    {
	this(messageType, interfaceType, currentIpAddress, 
	     currentSubnetMask, currentGateway, new INTEGER(result));
    }
    
    /**
     * Construct with required components.
     */
    public SetInterfaceIpResponse(ContentMessageType messageType, 
		    InterfaceType interfaceType, IPAddress currentIpAddress, 
		    IPAddress currentSubnetMask, long result)
    {
	setMessageType(messageType);
	setInterfaceType(interfaceType);
	setCurrentIpAddress(currentIpAddress);
	setCurrentSubnetMask(currentSubnetMask);
	setResult(result);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
	mComponents[1] = InterfaceType.management;
	mComponents[2] = new IPAddress();
	mComponents[3] = new IPAddress();
	mComponents[4] = new IPAddress();
	mComponents[5] = new INTEGER();
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[6];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    case 1:
		return InterfaceType.management;
	    case 2:
		return new IPAddress();
	    case 3:
		return new IPAddress();
	    case 4:
		return new IPAddress();
	    case 5:
		return new INTEGER();
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    // Methods for field "interfaceType"
    public InterfaceType getInterfaceType()
    {
	return (InterfaceType)mComponents[1];
    }
    
    public void setInterfaceType(InterfaceType interfaceType)
    {
	mComponents[1] = interfaceType;
    }
    
    
    // Methods for field "currentIpAddress"
    public IPAddress getCurrentIpAddress()
    {
	return (IPAddress)mComponents[2];
    }
    
    public void setCurrentIpAddress(IPAddress currentIpAddress)
    {
	mComponents[2] = currentIpAddress;
    }
    
    
    // Methods for field "currentSubnetMask"
    public IPAddress getCurrentSubnetMask()
    {
	return (IPAddress)mComponents[3];
    }
    
    public void setCurrentSubnetMask(IPAddress currentSubnetMask)
    {
	mComponents[3] = currentSubnetMask;
    }
    
    
    // Methods for field "currentGateway"
    public IPAddress getCurrentGateway()
    {
	return (IPAddress)mComponents[4];
    }
    
    public void setCurrentGateway(IPAddress currentGateway)
    {
	mComponents[4] = currentGateway;
    }
    
    public boolean hasCurrentGateway()
    {
	return componentIsPresent(4);
    }
    
    public void deleteCurrentGateway()
    {
	setComponentAbsent(4);
    }
    
    
    // Methods for field "result"
    public long getResult()
    {
	return ((INTEGER)mComponents[5]).longValue();
    }
    
    public void setResult(long result)
    {
	setResult(new INTEGER(result));
    }
    
    public void setResult(INTEGER result)
    {
	mComponents[5] = result;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "SetInterfaceIpResponse"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "SetInterfaceIpResponse"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryAlarm",
					8
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					9
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					10
				    ),
				    new MemberListElement (
					"sendPacketStats",
					11
				    ),
				    new MemberListElement (
					"receivePacketStats",
					12
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					13
				    ),
				    new MemberListElement (
					"hostMngService",
					14
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					15
				    ),
				    new MemberListElement (
					"iprouteService",
					16
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8001
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"InterfaceType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"InterfaceType"
			    ),
			    536603,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"management",
					0
				    ),
				    new MemberListElement (
					"business",
					1
				    )
				}
			    ),
			    0,
			    InterfaceType.management
			)
		    ),
		    "interfaceType",
		    1,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8002
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "currentIpAddress",
		    2,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8003
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "currentSubnetMask",
		    3,
		    2,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new ChoiceInfo (
			    new Tags (
				new short[] {
				    (short)0x8004
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"IPAddress"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"IPAddress"
			    ),
			    536603,
			    null,
			    new FieldsRef (
				new QName (
				    "com.unimas.asn.servicemanager.servicemanagementhttp",
				    "IPAddress"
				)
			    ),
			    0,
			    new TagDecoder (
				new TagDecoderElement[] {
				    new TagDecoderElement((short)0x8000, 0),
				    new TagDecoderElement((short)0x8001, 1)
				}
			    )
			)
		    ),
		    "currentGateway",
		    4,
		    3,
		    null
		),
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new IntegerInfo (
			    new Tags (
				new short[] {
				    (short)0x8005
				}
			    ),
			    new QName (
				"com.oss.asn1",
				"INTEGER"
			    ),
			    new QName (
				"builtin",
				"INTEGER"
			    ),
			    536603,
			    new ValueRangeConstraint (
				new AbstractBounds(
				    new com.oss.asn1.INTEGER(0), 
				    new com.oss.asn1.INTEGER(1000),
				    0
				)
			    ),
			    new Bounds (
				java.lang.Long.valueOf(0),
				java.lang.Long.valueOf(1000)
			    ),
			    null,
			    2
			)
		    ),
		    "result",
		    5,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8001, 1)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8002, 2)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8003, 3)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8004, 4),
			new TagDecoderElement((short)0x8005, 5)
		    }
		),
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8005, 5)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' SetInterfaceIpResponse object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' SetInterfaceIpResponse object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for SetInterfaceIpResponse
