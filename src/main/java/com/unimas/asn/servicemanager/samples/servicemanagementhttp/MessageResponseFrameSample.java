
/* THIS SAMPLE PROGRAM IS PROVIDED AS IS. THE SAMPLE PROGRAM AND ANY RESULTS
 * OBTAINED FROM IT ARE PROVIDED WITHOUT ANY WARRANTIES OR REPRESENTATIONS,
 * EXPRESS, IMPLIED OR STATUTORY. */

package com.unimas.asn.servicemanager.samples.servicemanagementhttp;

import java.util.Enumeration;
import java.io.PrintStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import com.oss.asn1.*;
import com.oss.util.*;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;

/**
 * Define sample code for the MessageResponseFrame ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see com.unimas.asn.servicemanager.servicemanagementhttp.MessageResponseFrame
 */

public class MessageResponseFrameSample extends SampleUtil {

    /**
     * The default constructor. The class is not instantiable.
     */
    private MessageResponseFrameSample() {}

    /**
     * Create Sample Value.
     */
    public static MessageResponseFrame createSampleValue()
    {
	MessageResponseFrame value = new MessageResponseFrame();
	value.setVersion(new Uint8(1));
	value.setContent(new MessageResponseFrame.Content());
	{
	    MessageResponseFrame.Content content_2 = value.getContent();
	    content_2.setSetInterfaceIpResponse(new SetInterfaceIpResponse());
	    {
		SetInterfaceIpResponse setInterfaceIpResponse_3 = (SetInterfaceIpResponse)content_2.getChosenValue();
		setInterfaceIpResponse_3.setMessageType(ContentMessageType.setInterfaceIpService);
		setInterfaceIpResponse_3.setInterfaceType(InterfaceType.management);
		setInterfaceIpResponse_3.setCurrentIpAddress(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpResponse_3.setCurrentSubnetMask(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpResponse_3.setCurrentGateway(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpResponse_3.setResult(new com.oss.asn1.INTEGER(0));
	    }
	}
	return value;
    }
    
    public static void printValue(MessageResponseFrame value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("version ");
	    s.print(value.getVersion().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("content ");
	    printValue(value.getContent(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(MessageResponseFrame.Content value, PrintStream s)
    {
	
	switch (value.getChosenFlag()) {
	case MessageResponseFrame.Content.setInterfaceIpResponse_chosen:
	    s.print("setInterfaceIpResponse : ");
	    printValue(((SetInterfaceIpResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.serviceConfigResponse_chosen:
	    s.print("serviceConfigResponse : ");
	    printValue(((ServiceConfigResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.serviceControResponse_chosen:
	    s.print("serviceControResponse : ");
	    printValue(((ServiceControlResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.serviceStatusQueryResponse_chosen:
	    s.print("serviceStatusQueryResponse : ");
	    printValue(((ServiceStatusQueryResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.serviceConfigQueryResponse_chosen:
	    s.print("serviceConfigQueryResponse : ");
	    printValue(((ServiceConfigQueryResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.alarmReportResponse_chosen:
	    s.print("alarmReportResponse : ");
	    printValue(((AlarmReportResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.queryAlarmResponse_chosen:
	    s.print("queryAlarmResponse : ");
	    printValue(((QueryAlarmResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.workStatusResponse_chosen:
	    s.print("workStatusResponse : ");
	    printValue(((WorkStatusResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.getAllServiceIdsResponse_chosen:
	    s.print("getAllServiceIdsResponse : ");
	    printValue(((GetAllServiceIdsResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.sendPacketStatsResponse_chosen:
	    s.print("sendPacketStatsResponse : ");
	    printValue(((SendPacketStatsResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.receivePacketStatsResponse_chosen:
	    s.print("receivePacketStatsResponse : ");
	    printValue(((ReceivePacketStatsResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.checkCommStatusResponse_chosen:
	    s.print("checkCommStatusResponse : ");
	    printValue(((CheckCommStatusResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.queryHostMngResponse_chosen:
	    s.print("queryHostMngResponse : ");
	    printValue(((QueryHostMngResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.setHostMngResponse_chosen:
	    s.print("setHostMngResponse : ");
	    printValue(((SetHostMngResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.querySourceDeviceResponse_chosen:
	    s.print("querySourceDeviceResponse : ");
	    printValue(((QuerySourceDeviceResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.setSourceDeviceResponse_chosen:
	    s.print("setSourceDeviceResponse : ");
	    printValue(((SetSourceDeviceResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.queryRouteListResponse_chosen:
	    s.print("queryRouteListResponse : ");
	    printValue(((QueryRouteListResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.queryCurRouteResponse_chosen:
	    s.print("queryCurRouteResponse : ");
	    printValue(((QueryCurRouteResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.setRouteListResponse_chosen:
	    s.print("setRouteListResponse : ");
	    printValue(((SetRouteListResponse)value.getChosenValue()), s);
	    break;
	case MessageResponseFrame.Content.error_chosen:
	    s.print("error : ");
	    printValue(((ErrorResponse)value.getChosenValue()), s);
	    break;
	default:
	    s.print("<unknown choice>");
	}
    }
    public static void printValue(SetInterfaceIpResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("interfaceType ");
	    s.print(value.getInterfaceType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("currentIpAddress ");
	    printValue(value.getCurrentIpAddress(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("currentSubnetMask ");
	    printValue(value.getCurrentSubnetMask(), s);
	    if (value.hasCurrentGateway()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("currentGateway ");
		printValue(value.getCurrentGateway(), s);
	    }
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceConfigResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceControlResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceStartOrStop ");
	    s.print(value.getServiceStartOrStop().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceStatusQueryResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceStatus ");
	    s.print(value.getServiceStatus().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceConfigQueryResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("displayname ");
	    s.print(value.getDisplayname());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasProxyIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyIp ");
		printValue(value.getProxyIp(), s);
	    }
	    if (value.hasProxyPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyPort ");
		s.print(value.getProxyPort().longValue());
	    }
	    if (value.hasServerIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverIp ");
		printValue(value.getServerIp(), s);
	    }
	    if (value.hasServerPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverPort ");
		s.print(value.getServerPort().longValue());
	    }
	    if (value.hasContentKeyCheck()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("contentKeyCheck ");
		s.print(value.getContentKeyCheck());
	    }
	    if (value.hasProtocolFilter()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("protocolFilter ");
		s.print(value.getProtocolFilter());
	    }
	    if (value.hasUnpassDeal()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("unpassDeal ");
		s.print(value.getUnpassDeal().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(AlarmReportResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryAlarmResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarms ");
	    printValue(value.getAlarms(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(WorkStatusResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("sendPkgNumToday ");
	    s.print(value.getSendPkgNumToday());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("sendPkgSizeToday ");
	    s.print(value.getSendPkgSizeToday());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("recvPkgNumToday ");
	    s.print(value.getRecvPkgNumToday());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("recvPkgSizeToday ");
	    s.print(value.getRecvPkgSizeToday());
	    if (value.hasDevTime()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("devTime ");
		s.print(value.getDevTime());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(GetAllServiceIdsResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceIds ");
	    printValue(value.getServiceIds(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SendPacketStatsResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasPeriod()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("period ");
		s.print(value.getPeriod().longValue());
	    }
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("packetStats ");
	    printValue(value.getPacketStats(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ReceivePacketStatsResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasPeriod()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("period ");
		s.print(value.getPeriod().longValue());
	    }
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("packetStats ");
	    printValue(value.getPacketStats(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(CheckCommStatusResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("isConnected ");
	    s.print(value.getIsConnected());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("connectionEventTime ");
	    s.print(value.getConnectionEventTime());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryHostMngResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("centerIP ");
	    printValue(value.getCenterIP(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("authPort ");
	    s.print(value.getAuthPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmPort ");
	    s.print(value.getAlarmPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("certMngPort ");
	    s.print(value.getCertMngPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("sgPort ");
	    s.print(value.getSgPort().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetHostMngResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("centerIP ");
	    printValue(value.getCenterIP(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("authPort ");
	    s.print(value.getAuthPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmPort ");
	    s.print(value.getAlarmPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("certMngPort ");
	    s.print(value.getCertMngPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("sgPort ");
	    s.print(value.getSgPort().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QuerySourceDeviceResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("currentDevices ");
	    printValue(value.getCurrentDevices(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetSourceDeviceResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("currentDevices ");
	    printValue(value.getCurrentDevices(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryRouteListResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("interfaceType ");
	    s.print(value.getInterfaceType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("routeList ");
	    printValue(value.getRouteList(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryCurRouteResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("interfaceType ");
	    s.print(value.getInterfaceType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("routeList ");
	    printValue(value.getRouteList(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetRouteListResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("interfaceType ");
	    s.print(value.getInterfaceType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("routeList ");
	    printValue(value.getRouteList(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("result ");
	    s.print(value.getResult());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ErrorResponse value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("errorState ");
	    s.print(value.getErrorState().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(IPAddress value, PrintStream s)
    {
	
	switch (value.getChosenFlag()) {
	case IPAddress.ipV4_chosen:
	    s.print("ipV4 : ");
	    s.print(((IPv4Address)value.getChosenValue()));
	    break;
	case IPAddress.ipV6_chosen:
	    s.print("ipV6 : ");
	    s.print(((IPv6Address)value.getChosenValue()));
	    break;
	default:
	    s.print("<unknown choice>");
	}
    }
    public static void printValue(QueryAlarmResponse.Alarms value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(GetAllServiceIdsResponse.ServiceIds value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		s.print(value.get(_index).longValue());
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SendPacketStatsResponse.PacketStats value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ReceivePacketStatsResponse.PacketStats value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QuerySourceDeviceResponse.CurrentDevices value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetSourceDeviceResponse.CurrentDevices value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryRouteListResponse.RouteList value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryCurRouteResponse.RouteList value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetRouteListResponse.RouteList value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(AlarmItem value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("alarmType ");
	    s.print(value.getAlarmType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmCode ");
	    s.print(value.getAlarmCode().longValue());
	    if (value.hasAlarmDesc()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("alarmDesc ");
		s.print("\"" + value.getAlarmDesc().stringValue() + "\"");
	    }
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmCount ");
	    s.print(value.getAlarmCount());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("packetType ");
	    s.print(value.getPacketType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("packetCount ");
	    s.print(value.getPacketCount().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SourceDevice value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("ipAddress ");
	    printValue(value.getIpAddress(), s);
	    if (value.hasPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("port ");
		s.print(value.getPort());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(com.unimas.asn.servicemanager.servicemanagementhttp.RouteList value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("destination ");
	    printValue(value.getDestination(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("subnetMask ");
	    printValue(value.getSubnetMask(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("gateway ");
	    printValue(value.getGateway(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("metric ");
	    s.print(value.getMetric());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    
    public static int encodeDecodeAndPrint(MessageResponseFrame value, int run)
    {
	Coder coder = com.unimas.asn.servicemanager.Servicemanager.getDefaultCoder();
	ByteArrayInputStream source;
	ByteArrayOutputStream sink;
	byte[] encoding = null;
	boolean passed = true;
	
	/* Print input value using AbstractData.toString() method*/
	System.out.println("\n--------------- Test run " + run + "---------------");
	System.out.println("\nEncoder input value:\n");
	System.out.print(value);
	
	/* Set coder properties */
	coder.enableEncoderDebugging();
	coder.enableDecoderDebugging();
	coder.enableEncoderConstraints();
	coder.enableDecoderConstraints();
	coder.enableAutomaticEncoding();
	coder.enableAutomaticDecoding();
	coder.enableContainedValueEncoding();
	coder.enableContainedValueDecoding();
	
	/* Encode the value */
	sink = new ByteArrayOutputStream();
	try {
	    System.out.print("\n\tTracing Information from Encoder...\n\n");
	    coder.encode(value, sink);
	    encoding = sink.toByteArray();
	    System.out.print("\nPDU successfully encoded, in " + encoding.length + " bytes:\n");
	    
	    if ((coder instanceof XERCoder)
		|| (coder instanceof CXERCoder)
		|| (coder instanceof EXERCoder)) {
		System.out.write(encoding, 0, encoding.length);
	    } else {
		HexTool.printHex(encoding);
	    }
	} catch(EncodeFailedException e) {
	    System.out.println("Encoding failed with return code = " + e.getReason());
	    System.out.print(e);
	    passed = false;
	} catch(EncodeNotSupportedException e) {
	    System.out.println("Encoding not supported for the value");
	    System.out.print(e);
	    passed = false;
	}
	
	if (!passed)
	    return 1;
	
	/* Decode the PDU that was just encoded */
	source = new ByteArrayInputStream(encoding);
	MessageResponseFrame decoded = null;
	try {
	    System.out.print("\n\tTracing Information from Decoder...\n\n");
	    decoded = (MessageResponseFrame)coder.decode(source, value);
	    System.out.print("\nPDU successfully decoded.\n");
	} catch (DecodeFailedException e) {
	    System.out.println("Decoding failed with return code = " + e.getReason());
	    System.out.print(e);
	    passed = false;
	} catch (DecodeNotSupportedException e) {
	    System.out.println("Decoding not supported for the value");
	    System.out.print(e);
	    passed = false;
	}
	
	if (!passed)
	    return 1;
	/* Print decoded value using sample printValue() method */
	System.out.print("\n\tDecoded PDU...\n\n");
	printValue(decoded, System.out);
	System.out.print("\n");
	
	return 0;
    }
    
    public static void main(String[] arg)
    {
	int run = 0;
	int failures = 0;
	
	failures += encodeDecodeAndPrint(createSampleValue(), ++run);
	newline(System.out, 0);
	
	if (failures > 0)
	    System.out.println(failures + " values failed.");
	else
	    System.out.println("All values encoded and decoded successfully.");
    }
    
}
