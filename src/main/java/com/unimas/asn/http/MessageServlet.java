package com.unimas.asn.http;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Enumeration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneId;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.unimas.asn.bean.UdpConfig;
import com.unimas.asn.codec.OssServAdapter;
import com.unimas.asn.logic.ServiceConfigLogic;
import com.unimas.asn.logic.ServiceConfigQueryLogic;
import com.unimas.asn.logic.ServiceStatusLogic;
import com.unimas.asn.logic.QueryRouteListLogic;
import com.unimas.asn.logic.QueryCurRouteLogic;
import com.unimas.asn.logic.SetRouteListLogic;
import com.unimas.asn.db.UdpAuditDAO;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.*;
import com.unimassystem.main.License;
import com.oss.asn1.BOOLEAN;
import com.oss.asn1.UTF8String16;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * 处理ASN.1消息的HTTP Servlet
 */
public class MessageServlet extends HttpServlet {
    private static final Logger logger = LoggerFactory.getLogger(MessageServlet.class);

    // 授权模块名称
    private static final String SG_BASE_MODEL = "baseModel";

    /**
     * 检查授权是否有效
     * @return 授权是否有效
     */
    private boolean isLicenseValid() {
        boolean ret = DeviceConfigReader.getInstance().getNetwork().equals(Network.sender);
        if(ret ){ //发送验证
            try {
                ret = new License().isValidTime(SG_BASE_MODEL);
            } catch (Exception e) {
                logger.error("检查授权时发生错误", e);
                ret =  false;
            }
        }else{ //接收不验证
            ret = true;
        }
        return ret;
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 检查授权是否有效
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理请求");
            sendHttpErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "授权已过期，请联系管理员");
            return;
        }
        try {
            // 读取请求体
            byte[] requestBody = readRequestBody(request);
            logger.info("request body is {}", Hex.toHexString(requestBody));
            // 解码请求
            MessageRequestFrame frame = OssServAdapter.decode(requestBody, MessageRequestFrame.class);
            logger.info("decode message frame is {}", frame);
            // 处理消息
            byte[] responseData = processMessage(frame);
            logger.info("response data is {}", Hex.toHexString(responseData));
            // 发送响应
            response.setContentType("application/octet-stream");
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }
        } catch (Exception e) {
            logger.error("Error processing request", e);
//            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            sendHttpErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器内部错误");
        }
    }

    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            throw new IOException("Invalid content length: " + contentLength);
        }

        byte[] buffer = new byte[contentLength];
        try (InputStream is = request.getInputStream()) {
            int totalBytesRead = 0;
            while (totalBytesRead < contentLength) {
                int bytesRead = is.read(buffer, totalBytesRead, contentLength - totalBytesRead);
                if (bytesRead == -1) {
                    break;
                }
                totalBytesRead += bytesRead;
            }
            if (totalBytesRead != contentLength) {
                throw new IOException("Expected " + contentLength + " bytes but read " + totalBytesRead + " bytes");
            }
            return buffer;
        }
    }

    private byte[] processMessage(MessageRequestFrame frame) throws IOException {
        // 再次检查授权是否有效，防止doPost方法被绕过
        if (!isLicenseValid()) {
            logger.error("授权已过期，拒绝处理消息");
            throw new IOException("授权已过期");
        }

        try {
            // 验证请求帧
            RequestValidator.validateRequestFrame(frame);

            MessageRequestFrame.Content content = frame.getContent();
            // 创建响应帧
            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(frame.getVersion());
            responseFrame.setContent(new MessageResponseFrame.Content());

            if (content.hasServiceConfigRequest()) {
                try {
                    ServiceConfigRequest request = content.getServiceConfigRequest();
                    // 验证服务配置请求
                    RequestValidator.validateServiceConfigRequest(request);
                    ServiceConfigResponse serviceConfigResponse = processServiceConfigRequest(request);
                    responseFrame.getContent().setServiceConfigResponse(serviceConfigResponse);
                } catch (ValidationException e) {
                    // 创建验证错误响应
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceControlRequest()) {
                try {
                    ServiceControlRequest request = content.getServiceControlRequest();
                    // 验证服务控制请求
                    RequestValidator.validateServiceControlRequest(request);
                    ServiceControlResponse serviceControlResponse = processServiceControlRequest(request);
                    responseFrame.getContent().setServiceControlResponse(serviceControlResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务控制请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceStatusQueryRequest()) {
                try {
                    ServiceStatusQueryRequest request = content.getServiceStatusQueryRequest();
                    // 验证服务状态查询请求
                    RequestValidator.validateServiceStatusQueryRequest(request);
                    ServiceStatusQueryResponse serviceStatusQueryResponse = processServiceStatusQueryRequest(request);
                    responseFrame.getContent().setServiceStatusQueryResponse(serviceStatusQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务状态查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasServiceConfigQueryRequest()) {
                try {
                    ServiceConfigQueryRequest request = content.getServiceConfigQueryRequest();
                    // 验证服务配置查询请求
                    RequestValidator.validateServiceConfigQueryRequest(request);
                    ServiceConfigQueryResponse serviceConfigQueryResponse = processServiceConfigQueryRequest(request);
                    responseFrame.getContent().setServiceConfigQueryResponse(serviceConfigQueryResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("服务配置查询请求验证失败: {}", e.getMessage());
                }
            } else if (content.hasAlarmReportRequest()) {
                AlarmReportRequest request = content.getAlarmReportRequest();
                // 验证报警报告请求
//                    RequestValidator.validateAlarmReportRequest(request);
                AlarmReportResponse alarmReportResponse = processAlarmReportRequest(request);
                responseFrame.getContent().setAlarmReportResponse(alarmReportResponse);
            } else if (content.hasWorkStatusRequest()) {
                try {
                    WorkStatusRequest request = content.getWorkStatusRequest();
                    // 验证工作状态请求
                    RequestValidator.validateWorkStatusRequest(request);
                    WorkStatusResponse workStatusResponse = processWorkStatusRequest(request);
                    responseFrame.getContent().setWorkStatusResponse(workStatusResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("工作状态请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasGetAllServiceIdsRequest()){
                GetAllServiceIdsRequest request = content.getGetAllServiceIdsRequest();
                GetAllServiceIdsResponse getAllServiceIdsResponse = processGetAllServiceIdsRequest(request);
                responseFrame.getContent().setGetAllServiceIdsResponse(getAllServiceIdsResponse);
            } else if(content.hasSendPacketStatsRequest()){
                try {
                    SendPacketStatsRequest request = content.getSendPacketStatsRequest();
                    // 验证发送包统计请求
                    RequestValidator.validateSendPacketStatsRequest(request);
                    SendPacketStatsResponse sendPacketStatsResponse = processSendPacketStatsRequest(request);
                    responseFrame.getContent().setSendPacketStatsResponse(sendPacketStatsResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("发送包统计请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasReceivePacketStatsRequest()){
                try {
                    ReceivePacketStatsRequest request = content.getReceivePacketStatsRequest();
                    // 验证接收包统计请求
                    RequestValidator.validateReceivePacketStatsRequest(request);
                    ReceivePacketStatsResponse receivePacketStatsResponse = processReceivePacketStatsRequest(request);
                    responseFrame.getContent().setReceivePacketStatsResponse(receivePacketStatsResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("接收包统计请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasCheckCommStatusRequest()){

                CheckCommStatusRequest checkCommStatusRequest = content.getCheckCommStatusRequest();
                CheckCommStatusResponse checkCommStatusResponse = processCheckCommStatusRequest(checkCommStatusRequest);
                responseFrame.getContent().setCheckCommStatusResponse(checkCommStatusResponse);
            } else if(content.hasSetInterfaceIpRequest()){
                try {
                    SetInterfaceIpRequest request = content.getSetInterfaceIpRequest();
                    // 验证设置网口IP请求
                    RequestValidator.validateSetInterfaceIpRequest(request);
                    SetInterfaceIpResponse setInterfaceIpResponse = processSetInterfaceIpRequest(request);
                    responseFrame.getContent().setSetInterfaceIpResponse(setInterfaceIpResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置网口IP请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQueryHostMngRequest()){
                try {
                    QueryHostMngRequest request = content.getQueryHostMngRequest();
                    // 验证查询上位机管理请求
                    RequestValidator.validateQueryHostMngRequest(request);
                    QueryHostMngResponse queryHostMngResponse = processQueryHostMngRequest(request);
                    responseFrame.getContent().setQueryHostMngResponse(queryHostMngResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询上位机管理请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasSetHostMngRequest()){
                try {
                    SetHostMngRequest request = content.getSetHostMngRequest();
                    // 验证设置上位机管理请求
                    RequestValidator.validateSetHostMngRequest(request);
                    SetHostMngResponse setHostMngResponse = processSetHostMngRequest(request);
                    responseFrame.getContent().setSetHostMngResponse(setHostMngResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置上位机管理请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQuerySourceDeviceRequest()){
                try {
                    QuerySourceDeviceRequest request = content.getQuerySourceDeviceRequest();
                    // 验证查询源设备请求
                    RequestValidator.validateQuerySourceDeviceRequest(request);
                    QuerySourceDeviceResponse querySourceDeviceResponse = processQuerySourceDeviceRequest(request);
                    responseFrame.getContent().setQuerySourceDeviceResponse(querySourceDeviceResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询源设备请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasSetSourceDeviceRequest()){
                try {
                    SetSourceDeviceRequest request = content.getSetSourceDeviceRequest();
                    // 验证设置源设备请求
                    RequestValidator.validateSetSourceDeviceRequest(request);
                    SetSourceDeviceResponse setSourceDeviceResponse = processSetSourceDeviceRequest(request);
                    responseFrame.getContent().setSetSourceDeviceResponse(setSourceDeviceResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置源设备请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQueryRouteListRequest()){
                try {
                    QueryRouteListRequest request = content.getQueryRouteListRequest();
                    // 验证查询路由列表请求
                    RequestValidator.validateQueryRouteListRequest(request);
                    QueryRouteListResponse queryRouteListResponse = processQueryRouteListRequest(request);
                    responseFrame.getContent().setQueryRouteListResponse(queryRouteListResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询路由列表请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasQueryCurRouteRequest()){
                try {
                    QueryCurRouteRequest request = content.getQueryCurRouteRequest();
                    // 验证查询当前路由请求
                    RequestValidator.validateQueryCurRouteRequest(request);
                    QueryCurRouteResponse queryCurRouteResponse = processQueryCurRouteRequest(request);
                    responseFrame.getContent().setQueryCurRouteResponse(queryCurRouteResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("查询当前路由请求验证失败: {}", e.getMessage());
                }
            } else if(content.hasSetRouteListRequest()){
                try {
                    SetRouteListRequest request = content.getSetRouteListRequest();
                    // 验证设置路由列表请求
                    RequestValidator.validateSetRouteListRequest(request);
                    SetRouteListResponse setRouteListResponse = processSetRouteListRequest(request);
                    responseFrame.getContent().setSetRouteListResponse(setRouteListResponse);
                } catch (ValidationException e) {
                    ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
                    responseFrame.getContent().setError(errorResponse);
                    logger.error("设置路由列表请求验证失败: {}", e.getMessage());
                }
            }else {
                // 未知的请求类型
                ErrorResponse errorResponse = createErrorResponse( null, ProcessErrorState.messageStructureError);
                responseFrame.getContent().setError(errorResponse);
                logger.error("收到未知的请求类型");
            }

            logger.info("response frame is {}", responseFrame);
            // 编码响应
            return OssServAdapter.encode(responseFrame);
        } catch (ValidationException e) {
            // 处理顶层验证异常
            MessageResponseFrame errorFrame = createErrorResponseFrame(frame.getVersion().intValue(), e);
            logger.error("请求帧验证失败: {}", e.getMessage());
            return OssServAdapter.encode(errorFrame);
        }
    }

    /**
     * 创建带有错误信息的响应帧
     */
    private MessageResponseFrame createErrorResponseFrame(int version, ValidationException e) {
        MessageResponseFrame responseFrame = new MessageResponseFrame();
        responseFrame.setVersion(new Uint8(version));
        responseFrame.setContent(new MessageResponseFrame.Content());
        ErrorResponse errorResponse = createErrorResponse(e.getErrorMessageContentType(), e.getErrorCode());
        responseFrame.getContent().setError(errorResponse);
        return responseFrame;
    }

    /**
     * 创建错误响应对象
     */
    private ErrorResponse createErrorResponse(ContentMessageType messageType, ProcessErrorState code) {
        ErrorResponse error = new ErrorResponse();
        error.setErrorState(code);
        error.setMessageType(messageType);
        return error;
    }

    private ServiceConfigResponse processServiceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        logger.info("Processing ServiceConfigRequest");
        ServiceConfigLogic serviceConfigLogic = new ServiceConfigLogic();
        return serviceConfigLogic.serviceConfigRequest(request);
    }

    private ServiceControlResponse processServiceControlRequest(ServiceControlRequest request) throws ValidationException {
        logger.info("Processing ServiceControlRequest for service: {}, action: {}",
            request.getServiceId(), request.getServiceStartOrStop());
        ServiceStatusLogic serviceStatusLogic = new ServiceStatusLogic();
        return serviceStatusLogic.startOrStop(request);
    }

    private ServiceStatusQueryResponse processServiceStatusQueryRequest(ServiceStatusQueryRequest request) {
        logger.info("Processing ServiceStatusQueryRequest for service: {}", request.getServiceId());
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        int runFlag = 0;
        try {
            runFlag = configXmlOperator.getIsRun(request.getServiceId().intValue()+"")?0:1;
        } catch (Exception e) {
            logger.error("Error processing ServiceStatusQueryRequest", e);
            runFlag = 2;
        }
        ServiceStatusQueryResponse response = new ServiceStatusQueryResponse();
        response.setMessageType(ContentMessageType.queryServiceStatus);
        response.setServiceId(request.getServiceId());
        response.setServiceStatus(ServiceStatus.valueOf(runFlag)); // 示例状态
        return response;
    }

    private ServiceConfigQueryResponse processServiceConfigQueryRequest(ServiceConfigQueryRequest request) {
        logger.info("Processing ServiceConfigQueryRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());
        return new ServiceConfigQueryLogic().serviceConfigQuery(request);
    }

    private AlarmReportResponse processAlarmReportRequest(AlarmReportRequest request) {
        logger.info("alarm info = " + request);
        AlarmReportResponse response = new AlarmReportResponse();
        response.setMessageType(ContentMessageType.reportAlarm);
        response.setServiceId(request.getServiceId());
        return response;
    }

    private GetAllServiceIdsResponse processGetAllServiceIdsRequest(GetAllServiceIdsRequest request) {
        logger.info("Processing GetAllServiceIdsRequest");

        // 创建响应对象
        GetAllServiceIdsResponse response = new GetAllServiceIdsResponse();
        response.setMessageType(ContentMessageType.getAllServiceIds);

        // 创建服务ID集合
        GetAllServiceIdsResponse.ServiceIds serviceIds = new GetAllServiceIdsResponse.ServiceIds();

        try {
            // 使用ConfigXmlOperator获取所有服务ID
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);

            // 使用新增的getAllServiceIds方法获取所有服务ID
            List<Integer> allServiceIds = configXmlOperator.getAllServiceIds();
            // 将服务ID添加到响应中
            for (Integer id : allServiceIds) {
                serviceIds.add(new ServiceId(id));
            }
            // 设置服务ID列表到响应
            response.setServiceIds(serviceIds);
        } catch (Exception e) {
            logger.error("Error processing GetAllServiceIdsRequest", e);
            // 发生错误时返回空列表
            response.setServiceIds(serviceIds);
        }

        return response;
    }

    /**
     * 处理检查通信状态请求
     * 使用NetworkLinkStatusReader工具类获取网卡状态
     */
    private CheckCommStatusResponse processCheckCommStatusRequest(CheckCommStatusRequest request) {
        logger.info("Processing CheckCommStatusRequest for service: {}, network: {}",
            request.getServiceId(), request.getNetwork());

        // 创建响应对象
        CheckCommStatusResponse response = new CheckCommStatusResponse();
        response.setMessageType(request.getMessageType());
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        try {
            // 使用NetworkLinkStatusReader获取网卡状态
            NetworkLinkStatusReader.LinkStatus linkStatus = NetworkLinkStatusReader.getLastLinkStatus();

            if (linkStatus != null) {
                // 设置连接状态
                boolean isConnected = "UP".equals(linkStatus.getStatus());
                response.setIsConnected(isConnected);

                // 设置连接事件时间
                LocalDateTime timestamp = linkStatus.getTimestamp();
                if (timestamp != null) {
                    // 将LocalDateTime转换为毫秒时间戳
                    long epochMilli = timestamp.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(epochMilli))));
                } else {
                    // 如果时间戳为空，使用当前时间
                    response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                }

                logger.info("Network link status: {}, timestamp: {}", linkStatus.getStatus(), timestamp);
            } else {
                // 如果无法获取网卡状态，默认为断开状态
                response.setIsConnected(false);
                response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
                logger.warn("Unable to get network link status, defaulting to disconnected");
            }

        } catch (Exception e) {
            // 发生异常时，默认为断开状态
            response.setIsConnected(false);
            response.setConnectionEventTime(new Uint64(new BigInteger(String.valueOf(System.currentTimeMillis()))));
            logger.error("Error processing CheckCommStatusRequest", e);
        }

        return response;
    }

    private WorkStatusResponse processWorkStatusRequest(WorkStatusRequest request) {
        logger.info("Processing WorkStatusRequest for service: {}", request.getServiceId());
        WorkStatusResponse response = new WorkStatusResponse();
        response.setMessageType(ContentMessageType.queryWorkStatus);
        response.setServiceId(request.getServiceId());

        try {
            // 从数据库udp_audit表查询统计数据
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();
            String serviceId = request.getServiceId().intValue() + "";

            // 获取当天发送包统计数据（基于count字段为1且begintime为当天的记录）
            Map<String, BigInteger> sendStats = udpAuditDAO.getSendStatsByServiceId(serviceId);
            BigInteger sendPkgNum = sendStats.get("totalCount");
            BigInteger sendPkgSize = sendStats.get("totalBytes");

            // 获取当天接收包统计数据（基于count_r字段为1且begintime为当天的记录）
            Map<String, BigInteger> recvStats = udpAuditDAO.getRecvStatsByServiceId(serviceId);
            BigInteger recvPkgNum = recvStats.get("totalCount");
            BigInteger recvPkgSize = recvStats.get("totalBytes");

            // 设置实际数据，直接使用BigInteger构造Uint64
            response.setSendPkgNumToday(new Uint64(sendPkgNum));
            response.setSendPkgSizeToday(new Uint64(sendPkgSize));
            response.setRecvPkgNumToday(new Uint64(recvPkgNum));
            response.setRecvPkgSizeToday(new Uint64(recvPkgSize));

            logger.info("Database stats for service {}: send packets={}, send size={}, recv packets={}, recv size={}",
                    serviceId, sendPkgNum, sendPkgSize, recvPkgNum, recvPkgSize);
        } catch (Exception e) {
            logger.error("Error getting database stats, using default values", e);
            // 出错时使用默认值
            response.setSendPkgNumToday(new Uint64(BigInteger.ZERO));
            response.setSendPkgSizeToday(new Uint64(BigInteger.ZERO));
            response.setRecvPkgNumToday(new Uint64(BigInteger.ZERO));
            response.setRecvPkgSizeToday(new Uint64(BigInteger.ZERO));
        }

        // 设置设备时间
        Uint64 l = new Uint64();
        l.setValue(new BigInteger(String.valueOf(System.currentTimeMillis())));
        response.setDevTime(l);
        return response;
    }

    /**
     * 处理发送包统计请求
     * @param request 发送包统计请求
     * @return 发送包统计响应
     */
    private SendPacketStatsResponse processSendPacketStatsRequest(SendPacketStatsRequest request) {
        logger.info("Processing SendPacketStatsRequest for service: {}, network: {}",
                request.getServiceId(), request.getNetwork());

        // 创建响应对象
        SendPacketStatsResponse response = new SendPacketStatsResponse();
        response.setMessageType(ContentMessageType.sendPacketStats);
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        // 如果请求中有period，则设置到响应中
        if (request.hasPeriod()) {
            response.setPeriod(request.getPeriod());
        }

        try {
            // 创建UdpAuditDAO实例
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();

            // 获取包统计数据
            List<PacketStats> packetStatsList;
            String serviceId = request.getServiceId().intValue()+"";

            if (request.hasPeriod()) {
                // 如果有时间段参数，按时间段查询
                int periodMinutes = request.getPeriod().intValue();
                packetStatsList = udpAuditDAO.getPacketStatsByServiceIdAndPeriod(serviceId, periodMinutes,true);
                logger.info("Fetched packet stats for service {} with period {} minutes: {} records",
                        serviceId, periodMinutes, packetStatsList.size());
            } else {
                // 否则查询所有数据
                packetStatsList = udpAuditDAO.getPacketStatsByServiceId(serviceId);
                logger.info("Fetched packet stats for service {}: {} records",
                        serviceId, packetStatsList.size());
            }

            // 创建PacketStats集合并设置数据
            SendPacketStatsResponse.PacketStats responsePacketStats = new SendPacketStatsResponse.PacketStats();

            // 将查询结果添加到响应中
            for (com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats stats : packetStatsList) {
                responsePacketStats.add(stats);
            }

            response.setPacketStats(responsePacketStats);

            logger.info("Successfully processed SendPacketStatsRequest for service: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error processing SendPacketStatsRequest for service: {}",
                    request.getServiceId(), e);

            // 发生错误时返回空的统计数据
            SendPacketStatsResponse.PacketStats emptyPacketStats = new SendPacketStatsResponse.PacketStats();
            response.setPacketStats(emptyPacketStats);
        }

        return response;
    }

    /**
     * 处理接收包统计请求
     * @param request 接收包统计请求
     * @return 接收包统计响应
     */
    private ReceivePacketStatsResponse processReceivePacketStatsRequest(ReceivePacketStatsRequest request) {
        logger.info("Processing ReceivePacketStatsRequest for service: {}, network: {}",
                request.getServiceId(), request.getNetwork());

        // 创建响应对象
        ReceivePacketStatsResponse response = new ReceivePacketStatsResponse();
        response.setMessageType(ContentMessageType.receivePacketStats);
        response.setServiceId(request.getServiceId());
        response.setNetwork(request.getNetwork());

        // 如果请求中有period，则设置到响应中
        if (request.hasPeriod()) {
            response.setPeriod(request.getPeriod());
        }

        try {
            // 创建UdpAuditDAO实例
            UdpAuditDAO udpAuditDAO = new UdpAuditDAO();

            // 获取包统计数据
            List<com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats> packetStatsList;
            String serviceId = request.getServiceId().intValue()+"";

            if (request.hasPeriod()) {
                // 如果有时间段参数，按时间段查询
                int periodMinutes = request.getPeriod().intValue();
                packetStatsList = udpAuditDAO.getPacketStatsByServiceIdAndPeriod(serviceId, periodMinutes,false);
                logger.info("Fetched receive packet stats for service {} with period {} minutes: {} records",
                        serviceId, periodMinutes, packetStatsList.size());
            } else {
                // 否则查询所有数据
                packetStatsList = udpAuditDAO.getPacketStatsByServiceId(serviceId);
                logger.info("Fetched receive packet stats for service {}: {} records",
                        serviceId, packetStatsList.size());
            }

            // 创建PacketStats集合并设置数据
            ReceivePacketStatsResponse.PacketStats responsePacketStats = new ReceivePacketStatsResponse.PacketStats();

            // 将查询结果添加到响应中
            for (com.unimas.asn.servicemanager.servicemanagementhttp.PacketStats stats : packetStatsList) {
                responsePacketStats.add(stats);
            }

            response.setPacketStats(responsePacketStats);

            logger.info("Successfully processed ReceivePacketStatsRequest for service: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error processing ReceivePacketStatsRequest for service: {}",
                    request.getServiceId(), e);

            // 发生错误时返回空的统计数据
            ReceivePacketStatsResponse.PacketStats emptyPacketStats = new ReceivePacketStatsResponse.PacketStats();
            response.setPacketStats(emptyPacketStats);
        }

        return response;
    }

    /**
     * 处理设置网口IP请求（专门用于修改Debian系统网卡地址）
     * @param request 设置网口IP请求
     * @return 设置网口IP响应
     */
    private SetInterfaceIpResponse processSetInterfaceIpRequest(SetInterfaceIpRequest request) {
        logger.info("Processing SetInterfaceIpRequest for interface: {} on Debian system", request.getInterfaceType());

        // 创建响应对象
        SetInterfaceIpResponse response = new SetInterfaceIpResponse();
        response.setMessageType(ContentMessageType.setInterfaceIpService);
        response.setInterfaceType(request.getInterfaceType());

        boolean success = false;

        try {
            // 获取请求中的配置值
            String requestIP = IPUtil.ipAddressToString(request.getIpAddress());
            String requestSubnetMask = IPUtil.ipAddressToString(request.getSubnetMask());
            String requestGateway = request.hasGateway() ? IPUtil.ipAddressToString(request.getGateway()) : "";

            logger.info("Requested interface config - IP: {}, SubnetMask: {}, Gateway: {}",
                    requestIP, requestSubnetMask, requestGateway);

            // 网关设置不做限制，直接覆盖本网卡的网关
            if (requestGateway != null && !requestGateway.isEmpty()) {
                String targetInterface = (request.getInterfaceType() == InterfaceType.management) ? "eth0" : "eth1";

                // 如果本网卡已经有网关，允许修改（会覆盖现有网关）
                if (hasGatewayOnInterface(targetInterface)) {
                    logger.info("Gateway already exists on {}, will be updated with new gateway: {}", targetInterface, requestGateway);
                } else {
                    logger.info("Setting new gateway on {}: {}", targetInterface, requestGateway);
                }
            }

            // 根据接口类型处理
            if (request.getInterfaceType() == InterfaceType.management) {
                // 管理口配置 - 操作eth0虚地址（Debian系统）
                logger.info("Managing virtual address on eth0 interface (Debian system)");

                // 在Debian系统上操作eth0虚地址
                success = manageEth0VirtualAddress(requestIP, requestSubnetMask, requestGateway);

                if (success) {
                    logger.info("Successfully managed virtual address on eth0: {}", requestIP);
                } else {
                    logger.error("Failed to manage virtual address on eth0");
                }
            } else if (request.getInterfaceType() == InterfaceType.business) {
                // 业务口配置 - 修改eth1网卡IP（Debian系统）
                logger.info("Updating business interface (eth1) on Debian system");

                // 在Debian系统上修改eth1网卡配置
                success = updateEth1InterfaceOnDebian(requestIP, requestSubnetMask, requestGateway);

                if (success) {
                    logger.info("Successfully updated business interface (eth1) IP to: {}", requestIP);
                } else {
                    logger.error("Failed to update business interface (eth1) IP");
                }
            }

            // 获取当前网卡配置作为响应
            String targetInterface = (request.getInterfaceType() == InterfaceType.management) ? "eth0" : "eth1";
            String currentIP;

            // 对于管理接口，获取虚地址；对于业务接口，获取主地址
            if (request.getInterfaceType() == InterfaceType.management) {
                // 对于管理接口，使用请求中的IP作为当前IP（因为我们刚刚设置了虚地址）
                currentIP = requestIP;
            } else {
                // 对于业务接口，获取实际的当前IP
                currentIP = getCurrentInterfaceIP(targetInterface);
            }

            if (currentIP != null) {
                response.setCurrentIpAddress(IPUtil.createIPAddress(currentIP));
                response.setCurrentSubnetMask(request.getSubnetMask()); // 使用请求中的值
                if (request.hasGateway()) {
                    response.setCurrentGateway(request.getGateway());
                }
            } else {
                // 如果无法获取当前IP，使用请求中的值
                response.setCurrentIpAddress(request.getIpAddress());
                response.setCurrentSubnetMask(request.getSubnetMask());
                if (request.hasGateway()) {
                    response.setCurrentGateway(request.getGateway());
                }
            }

            // 设置执行结果
            response.setResult(success ? 0 : 1);

            logger.info("SetInterfaceIpRequest processed, result: {}", success ? "success" : "failed");

        } catch (Exception e) {
            logger.error("Error processing SetInterfaceIpRequest", e);
            // 设置错误响应
            response.setCurrentIpAddress(request.getIpAddress());
            response.setCurrentSubnetMask(request.getSubnetMask());
            response.setResult(1); // 错误
        }

        // 如果配置修改成功，安排程序在1秒后退出
//        if (success && response.getResult() == 0) {
//            // 确定修改的网卡名称
//            String targetInterface = (request.getInterfaceType() == InterfaceType.management) ? "eth0:1" : "eth1";
//
//            logger.info("Network interface configuration updated successfully, scheduling application exit in 1 second");
//            new Thread(() -> {
//                try {
//                    Thread.sleep(1000); // 休眠1秒
//                    logger.info("Exiting application due to network interface configuration changes");
//
//                    // 重启指定网卡使配置生效
//                    boolean networkRestartSuccess = restartNetworkInterface(targetInterface);
//                    if (!networkRestartSuccess) {
//                        logger.warn("Failed to restart interface {}, persistent configuration saved, changes will take effect after system reboot", targetInterface);
//                    }
//
//                    // 退出程序
//                    System.exit(0);
//                } catch (InterruptedException e) {
//                    logger.error("Exit thread interrupted", e);
//                    Thread.currentThread().interrupt();
//                }
//            }).start();
//        }

        return response;
    }

    /**
     * 在Debian系统上管理eth0虚地址
     * @param ip 虚地址IP
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否成功
     */
    private boolean manageEth0VirtualAddress(String ip, String subnetMask, String gateway) {
        try {
            logger.info("Managing eth0 virtual address - IP: {}, SubnetMask: {}, Gateway: {}",
                    ip, subnetMask, gateway);

            // 更新配置文件中的虚地址配置（持久化）
            boolean configSuccess = SystemConfigManager.updateEth0VirtualAddressConfig(ip, subnetMask, gateway);
            if (!configSuccess) {
                logger.error("Failed to update eth0 virtual address configuration file");
                return false;
            }

            logger.info("Successfully updated eth0 virtual address configuration to persistent storage");
            return true;

        } catch (Exception e) {
            logger.error("Error managing eth0 virtual address", e);
            return false;
        }
    }


    /**
     * 在Debian系统上更新eth1网卡配置
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否成功
     */
    private boolean updateEth1InterfaceOnDebian(String ip, String subnetMask, String gateway) {
        return updateInterfaceOnDebian("eth1", ip, subnetMask, gateway);
    }

    /**
     * 在Debian系统上更新指定网卡配置的通用方法
     * @param interfaceName 网卡名称（eth0或eth1）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否成功
     */
    private boolean updateInterfaceOnDebian(String interfaceName, String ip, String subnetMask, String gateway) {
        try {
            logger.info("Updating {} interface on Debian - IP: {}, SubnetMask: {}, Gateway: {}",
                    interfaceName, ip, subnetMask, gateway);

            // 1. 持久化配置到 /etc/network/interfaces 文件
            boolean persistentConfigSuccess = updatePersistentNetworkConfig(interfaceName, ip, subnetMask, gateway);
            if (!persistentConfigSuccess) {
                logger.error("Failed to update persistent network configuration for {}", interfaceName);
                return false;
            }


            logger.info("Successfully updated {} interface configuration", interfaceName);
            return true;

        } catch (Exception e) {
            logger.error("Error updating {} interface on Debian", interfaceName, e);
            return false;
        }
    }

    /**
     * 更新持久化网络配置到 /etc/network/interfaces 文件
     * @param interfaceName 网卡名称
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @return 是否成功
     */
    private boolean updatePersistentNetworkConfig(String interfaceName, String ip, String subnetMask, String gateway) {
        try {
            // 使用SystemConfigManager的方法更新持久化配置
            if ("eth0".equals(interfaceName)) {
                return SystemConfigManager.updateEth0NetworkConfig(ip, subnetMask, gateway);
            } else if ("eth1".equals(interfaceName)) {
                return SystemConfigManager.updateEth1NetworkConfig(ip, subnetMask, gateway);
            } else {
                logger.error("Unsupported interface: {}", interfaceName);
                return false;
            }
        } catch (Exception e) {
            logger.error("Error updating persistent network config for {}", interfaceName, e);
            return false;
        }
    }


    /**
     * 检查指定网卡是否已设置网关
     * @param interfaceName 网卡名称（eth0或eth1）
     * @return 如果该网卡有网关返回true，否则返回false
     */
    private boolean hasGatewayOnInterface(String interfaceName) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "route", "show", "default");
            Process process = pb.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("dev " + interfaceName)) {
                        logger.info("Found default gateway on interface: {}", interfaceName);
                        return true;
                    }
                }
            }

            process.waitFor();
            return false;

        } catch (Exception e) {
            logger.error("Error checking gateway on interface: {}", interfaceName, e);
            return false;
        }
    }

    /**
     * 获取指定网卡的当前IP地址
     * @param interfaceName 网卡名称（eth0或eth1）
     * @return IP地址字符串，如果获取失败返回null
     */
    private String getCurrentInterfaceIP(String interfaceName) {
        try {
            NetworkInterface networkInterface = NetworkInterface.getByName(interfaceName);
            if (networkInterface != null) {
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (!addr.isLoopbackAddress() && addr.getAddress().length == 4) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to get current {} IP address", interfaceName, e);
        }
        return null;
    }

    /**
     * 处理查询上位机管理请求
     * @param request 查询上位机管理请求
     * @return 查询上位机管理响应
     */
    private QueryHostMngResponse processQueryHostMngRequest(QueryHostMngRequest request) {
        logger.info("Processing QueryHostMngRequest");

        // 创建响应对象
        QueryHostMngResponse response = new QueryHostMngResponse();
        response.setMessageType(ContentMessageType.hostMngService);

        try {
            // 从配置文件读取上位机配置
            Properties centerConfig = SystemConfigManager.getCurrentCenterConfig();
            String centerIP = centerConfig.getProperty("alarm.report.destination.ip", "127.0.0.1");
            String authPortStr = centerConfig.getProperty("auth.port", "8080");
            String alarmPortStr = centerConfig.getProperty("alarm.report.destination.port", "8080");
            String certMngPortStr = centerConfig.getProperty("server.port", "8080");
            String sgPortStr = centerConfig.getProperty("server.port", "8080");

            // 设置响应数据
            response.setCenterIP(IPUtil.createIPAddress(centerIP));
            response.setAuthPort(new PortNumber(parsePortSafely(authPortStr, 8080)));
            response.setAlarmPort(new PortNumber(parsePortSafely(alarmPortStr, 8080)));
            response.setCertMngPort(new PortNumber(parsePortSafely(certMngPortStr, 8080)));
            response.setSgPort(new PortNumber(parsePortSafely(sgPortStr, 8080)));

            logger.info("QueryHostMngRequest processed successfully - CenterIP: {}, AuthPort: {}, AlarmPort: {}, CertMngPort: {}, SgPort: {}",
                    centerIP, authPortStr, alarmPortStr, certMngPortStr, sgPortStr);

        } catch (Exception e) {
            logger.error("Error processing QueryHostMngRequest", e);
            // 设置默认值
            try {
                response.setCenterIP(IPUtil.createIPAddress("127.0.0.1"));
                response.setAuthPort(new PortNumber(8080));
                response.setAlarmPort(new PortNumber(8080));
                response.setCertMngPort(new PortNumber(8080));
                response.setSgPort(new PortNumber(8080));
            } catch (Exception ex) {
                logger.error("Error setting default values for QueryHostMngResponse", ex);
            }
        }

        return response;
    }

    /**
     * 处理设置上位机管理请求
     * @param request 设置上位机管理请求
     * @return 设置上位机管理响应
     */
    private SetHostMngResponse processSetHostMngRequest(SetHostMngRequest request) {
        logger.info("Processing SetHostMngRequest");

        // 创建响应对象
        SetHostMngResponse response = new SetHostMngResponse();
        response.setMessageType(ContentMessageType.hostMngService);

        boolean allSuccess = true;
        StringBuilder statusDescription = new StringBuilder();

        try {
            // 读取当前配置
            Properties props = SystemConfigManager.getCurrentCenterConfig();

            // 检查请求和原配置是否一致，一致时直接返回成功
            boolean configurationMatches = true;

            // 比较centerIP
            if (request.hasCenterIP()) {
                String requestCenterIP = IPUtil.ipAddressToString(request.getCenterIP());
                String currentCenterIP = props.getProperty("alarm.report.destination.ip", "127.0.0.1");
                if (!requestCenterIP.equals(currentCenterIP)) {
                    configurationMatches = false;
                    logger.debug("CenterIP differs: request={}, current={}", requestCenterIP, currentCenterIP);
                }
            }

            // 比较authPort
            if (request.hasAuthPort() && configurationMatches) {
                int requestAuthPort = request.getAuthPort().intValue();
                int currentAuthPort = parsePortSafely(props.getProperty("auth.port"), 8080);
                if (requestAuthPort != currentAuthPort) {
                    configurationMatches = false;
                    logger.debug("AuthPort differs: request={}, current={}", requestAuthPort, currentAuthPort);
                }
            }

            // 比较alarmPort
            if (request.hasAlarmPort() && configurationMatches) {
                int requestAlarmPort = request.getAlarmPort().intValue();
                int currentAlarmPort = parsePortSafely(props.getProperty("alarm.report.destination.port"), 8080);
                if (requestAlarmPort != currentAlarmPort) {
                    configurationMatches = false;
                    logger.debug("AlarmPort differs: request={}, current={}", requestAlarmPort, currentAlarmPort);
                }
            }

            // 比较certMngPort
            if (request.hasCertMngPort() && configurationMatches) {
                int requestCertMngPort = request.getCertMngPort().intValue();
                int currentCertMngPort = parsePortSafely(props.getProperty("server.port"), 8080);
                if (requestCertMngPort != currentCertMngPort) {
                    configurationMatches = false;
                    logger.debug("CertMngPort differs: request={}, current={}", requestCertMngPort, currentCertMngPort);
                }
            }

            // 比较sgPort
            if (request.hasSgPort() && configurationMatches) {
                int requestSgPort = request.getSgPort().intValue();
                int currentSgPort = parsePortSafely(props.getProperty("server.port"), 8080);
                if (requestSgPort != currentSgPort) {
                    configurationMatches = false;
                    logger.debug("SgPort differs: request={}, current={}", requestSgPort, currentSgPort);
                }
            }

            // 如果配置完全一致，直接返回成功
            if (configurationMatches) {
                logger.info("SetHostMngRequest configuration matches current settings, returning success without changes");

                // 设置响应数据为当前配置值
                String currentCenterIP = props.getProperty("alarm.report.destination.ip", "127.0.0.1");
                if("".equals(currentCenterIP)) {
                    currentCenterIP = "127.0.0.1";
                }
                response.setCenterIP(IPUtil.createIPAddress(currentCenterIP));

                int currentAuthPort = parsePortSafely(props.getProperty("auth.port"), 8080);
                response.setAuthPort(new PortNumber(currentAuthPort));

                int currentAlarmPort = parsePortSafely(props.getProperty("alarm.report.destination.port"), 8080);
                response.setAlarmPort(new PortNumber(currentAlarmPort));

                int currentCertMngPort = parsePortSafely(props.getProperty("server.port"), 8080);
                response.setCertMngPort(new PortNumber(currentCertMngPort));

                int currentSgPort = parsePortSafely(props.getProperty("server.port"), 8080);
                response.setSgPort(new PortNumber(currentSgPort));

                response.setResult(0); // 成功

                logger.info("SetHostMngRequest processed successfully - no configuration changes needed");
                return response;
            }

            logger.info("SetHostMngRequest configuration differs from current settings, proceeding with update");

            // 先检查要设置的端口是否被占用
            List<Integer> portsToCheck = new ArrayList<>();

            if (request.hasCertMngPort()) {
                int certMngPort = request.getCertMngPort().intValue();
                portsToCheck.add(certMngPort);
            }
            if (request.hasSgPort()) {
                int sgPort = request.getSgPort().intValue();
                portsToCheck.add(sgPort);
            }

            // 检查端口占用情况
            for (Integer port : portsToCheck) {
                if (isPortInUse(port)) {
                    logger.error("Port {} is already in use, cannot update configuration", port);
                    
                    // 端口被占用，直接返回失败
                    response.setResult(1); // 失败
                    
                    // 填充当前配置作为响应
                    try {
                        String currentCenterIP = props.getProperty("alarm.report.destination.ip", "127.0.0.1");
                        response.setCenterIP(IPUtil.createIPAddress(currentCenterIP));

                        int currentAuthPort = parsePortSafely(props.getProperty("auth.port"), 8080);
                        response.setAuthPort(new PortNumber(currentAuthPort));

                        int currentAlarmPort = parsePortSafely(props.getProperty("alarm.report.destination.port"), 8080);
                        response.setAlarmPort(new PortNumber(currentAlarmPort));

                        int currentCertMngPort = parsePortSafely(props.getProperty("server.port"), 8080);
                        response.setCertMngPort(new PortNumber(currentCertMngPort));

                        int currentSgPort = parsePortSafely(props.getProperty("server.port"), 8080);
                        response.setSgPort(new PortNumber(currentSgPort));

                    } catch (Exception ex) {
                        logger.error("Error setting current values for SetHostMngResponse", ex);
                    }
                    
                    logger.info("SetHostMngRequest failed due to port {} in use", port);
                    return response;
                }
            }

            // 所有端口都可用，继续更新配置
            // 更新配置（只更新提供的字段）
            if (request.hasCenterIP()) {
                String centerIP = IPUtil.ipAddressToString(request.getCenterIP());
                props.setProperty("alarm.report.destination.ip", centerIP);
                response.setCenterIP(request.getCenterIP());
                logger.info("Updated centerIP to: {}", centerIP);
            } else {
                // 使用当前值
                String currentCenterIP = props.getProperty("alarm.report.destination.ip", "127.0.0.1");
                response.setCenterIP(IPUtil.createIPAddress(currentCenterIP));
            }

            if (request.hasAuthPort()) {
                int authPort = request.getAuthPort().intValue();
                props.setProperty("auth.port", String.valueOf(authPort));
                response.setAuthPort(request.getAuthPort());
                logger.info("Updated authPort to: {}", authPort);
            } else {
                int currentAuthPort = parsePortSafely(props.getProperty("auth.port"), 8080);
                response.setAuthPort(new PortNumber(currentAuthPort));
            }

            if (request.hasAlarmPort()) {
                int alarmPort = request.getAlarmPort().intValue();
                props.setProperty("alarm.report.destination.port", String.valueOf(alarmPort));
                response.setAlarmPort(request.getAlarmPort());
                logger.info("Updated alarmPort to: {}", alarmPort);
            } else {
                int currentAlarmPort = parsePortSafely(props.getProperty("alarm.report.destination.port"), 8080);
                response.setAlarmPort(new PortNumber(currentAlarmPort));
            }

            if (request.hasCertMngPort()) {
                int certMngPort = request.getCertMngPort().intValue();
                props.setProperty("server.port", String.valueOf(certMngPort));
                response.setCertMngPort(request.getCertMngPort());
                logger.info("Updated certMngPort to: {}", certMngPort);
            } else {
                int currentCertMngPort = parsePortSafely(props.getProperty("server.port"), 8080);
                response.setCertMngPort(new PortNumber(currentCertMngPort));
            }

            if (request.hasSgPort()) {
                int sgPort = request.getSgPort().intValue();
                props.setProperty("server.port", String.valueOf(sgPort));
                response.setSgPort(request.getSgPort());
                logger.info("Updated sgPort to: {}", sgPort);
            } else {
                int currentSgPort = parsePortSafely(props.getProperty("server.port"), 8080);
                response.setSgPort(new PortNumber(currentSgPort));
            }
            // 根据alarm.report.destination.ip和alarm.report.destination.port的值设置alarm.report.enabled
            String alarmDestinationIp = props.getProperty("alarm.report.destination.ip");
            String alarmDestinationPort = props.getProperty("alarm.report.destination.port");

            // 当IP和端口都不为空时，启用告警报告；否则禁用
            boolean alarmEnabled = (alarmDestinationIp != null && !alarmDestinationIp.trim().isEmpty()) &&
                    (alarmDestinationPort != null && !alarmDestinationPort.trim().isEmpty());

            props.setProperty("alarm.report.enabled", String.valueOf(alarmEnabled));
            logger.info("Set alarm.report.enabled to: {} (IP: {}, Port: {})", alarmEnabled, alarmDestinationIp, alarmDestinationPort);

            // 保存配置到文件
            try (OutputStream os = new FileOutputStream("/etc/unimas/tomcat/conf/application.properties")) {
                props.store(os, "Updated by SetHostMngRequest");
                logger.info("Successfully saved host management configuration");
            }

            // 设置执行结果
            response.setResult(0); // 成功

            logger.info("SetHostMngRequest processed successfully");

        } catch (Exception e) {
            logger.error("Error processing SetHostMngRequest", e);
            allSuccess = false;
            response.setResult(1); // 失败

            // 设置默认响应值
            try {
                response.setCenterIP(IPUtil.createIPAddress("127.0.0.1"));
                response.setAuthPort(new PortNumber(8080));
                response.setAlarmPort(new PortNumber(8080));
                response.setCertMngPort(new PortNumber(8080));
                response.setSgPort(new PortNumber(8080));
            } catch (Exception ex) {
                logger.error("Error setting default values for SetHostMngResponse", ex);
            }
        }
        if(response.getCenterIP().getIpV4() == null || response.getCenterIP().getIpV4().byteArrayValue() == null){
            try {
                response.setCenterIP(IPUtil.createIPAddress("127.0.0.1"));
            } catch (Exception e) {
                logger.error("Error setting default values for SetHostMngResponse", e);
            }
        }
        // 如果有配置变更，安排程序退出
        if (allSuccess) {
            logger.info("Host management configuration updated successfully, scheduling application exit in 1 second");
            // 在新线程中执行延迟退出，避免阻塞响应
            new Thread(() -> {
                try {
                    Thread.sleep(1000); // 休眠1秒
                    logger.info("Exiting application due to host management configuration changes");
                    System.exit(0);
                } catch (InterruptedException e) {
                    logger.error("Exit thread interrupted", e);
                    Thread.currentThread().interrupt();
                }
            }).start();
        }

        return response;
    }

    /**
     * 安全地解析端口号，处理空字符串和无效值
     * @param portStr 端口字符串
     * @param defaultPort 默认端口号
     * @return 解析后的端口号
     */
    private int parsePortSafely(String portStr, int defaultPort) {
        if (portStr == null || portStr.trim().isEmpty()) {
            return defaultPort;
        }
        try {
            return Integer.parseInt(portStr.trim());
        } catch (NumberFormatException e) {
            logger.warn("Invalid port value '{}', using default port {}", portStr, defaultPort);
            return defaultPort;
        }
    }

    /**
     * 检查指定端口是否被占用（排除程序自己的监听端口）
     * @param port 要检查的端口号
     * @return 如果端口被占用返回true，否则返回false
     */
    private boolean isPortInUse(int port) {
        // 获取程序自己的监听端口（sgPort）
        int currentSgPort = SystemConfigManager.getCurrentManagementPort();

        // 如果要检查的端口就是程序自己的监听端口，则认为端口可用
        if (port == currentSgPort) {
            logger.debug("Port {} is the current server listening port (sgPort), considering it available", port);
            return false;
        }

        try (java.net.ServerSocket socket = new java.net.ServerSocket(port)) {
            socket.setReuseAddress(true);
            return false; // 端口可用
        } catch (java.io.IOException e) {
            return true; // 端口被占用
        }
    }

    /**
     * 处理查询源设备请求（查询服务配置中的源设备信息）
     * @param request 查询源设备请求
     * @return 查询源设备响应
     */
    private QuerySourceDeviceResponse processQuerySourceDeviceRequest(QuerySourceDeviceRequest request) {
        logger.info("Processing QuerySourceDeviceRequest for service: {} - querying source devices from config", request.getServiceId());

        // 创建响应对象
        QuerySourceDeviceResponse response = new QuerySourceDeviceResponse();
        response.setMessageType(ContentMessageType.sourceDeviceService);
        response.setServiceId(request.getServiceId());

        try {
            String serviceId = request.getServiceId().intValue() + "";

            // 使用ServiceXmlOperator获取源设备信息
            ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
            List<SourceDevice> sourceDevices = serviceXmlOperator.getSourceDevices();

            // 创建设备列表
            QuerySourceDeviceResponse.CurrentDevices currentDevices = new QuerySourceDeviceResponse.CurrentDevices();

            // 将源设备添加到响应中
            for (SourceDevice device : sourceDevices) {
                currentDevices.add(device);
            }

            response.setCurrentDevices(currentDevices);

            logger.info("QuerySourceDeviceRequest processed successfully for service: {}, found {} devices",
                    request.getServiceId(), sourceDevices.size());

        } catch (Exception e) {
            logger.error("Error processing QuerySourceDeviceRequest for service: {}", request.getServiceId(), e);
            // 返回空的设备列表
            QuerySourceDeviceResponse.CurrentDevices emptyDevices = new QuerySourceDeviceResponse.CurrentDevices();
            response.setCurrentDevices(emptyDevices);
        }

        return response;
    }

    /**
     * 处理设置源设备请求（修改服务配置中的源设备信息）
     * @param request 设置源设备请求
     * @return 设置源设备响应
     */
    private SetSourceDeviceResponse processSetSourceDeviceRequest(SetSourceDeviceRequest request) {
        logger.info("Processing SetSourceDeviceRequest for service: {} - updating source devices in config", request.getServiceId());

        // 创建响应对象
        SetSourceDeviceResponse response = new SetSourceDeviceResponse();
        response.setMessageType(ContentMessageType.sourceDeviceService);
        response.setServiceId(request.getServiceId());

        boolean success = false;

        try {
            String serviceId = request.getServiceId().intValue() + "";

            // 校验服务是否正在运行，运行中的服务不允许修改源设备配置
            if (isServiceRunning(serviceId)) {
                logger.warn("Service {} is currently running, cannot modify source device configuration", serviceId);
                response.setResult(1); // 失败

                // 返回当前的源设备列表（不做修改）
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
                List<SourceDevice> currentSourceDevices = serviceXmlOperator.getSourceDevices();

                SetSourceDeviceResponse.CurrentDevices currentDevices = new SetSourceDeviceResponse.CurrentDevices();
                for (SourceDevice device : currentSourceDevices) {
                    currentDevices.add(device);
                }
                response.setCurrentDevices(currentDevices);

                logger.info("SetSourceDeviceRequest rejected - service {} is running", serviceId);
                return response;
            }

            // 获取源设备列表
            SetSourceDeviceRequest.Sourcedevices sourcedevices = request.getSourcedevices();

            if (sourcedevices != null) {
                logger.info("Updating source devices for service: {}, device count: {}", serviceId, sourcedevices.size());

                // 将请求中的源设备转换为List<SourceDevice>
                List<SourceDevice> sourceDeviceList = new ArrayList<>();
                for (int i = 0; i < sourcedevices.size(); i++) {
                    sourceDeviceList.add(sourcedevices.get(i));
                }

                // 使用ServiceXmlOperator更新源设备配置
                ServiceXmlOperator serviceXmlOperator = new ServiceXmlOperator(serviceId);
                success = serviceXmlOperator.updateSourceDevices(sourceDeviceList);

                if (success) {
                    logger.info("Successfully updated source devices for service: {}", serviceId);
                } else {
                    logger.error("Failed to update source devices for service: {}", serviceId);
                }
            } else {
                logger.warn("No source devices provided for service: {}", serviceId);
                success = true; // 空列表也算成功
            }

            // 创建响应中的当前设备列表
            SetSourceDeviceResponse.CurrentDevices currentDevices = new SetSourceDeviceResponse.CurrentDevices();

            // 将请求中的设备复制到响应中
            if (sourcedevices != null) {
                for (int i = 0; i < sourcedevices.size(); i++) {
                    currentDevices.add(sourcedevices.get(i));
                }
            }

            response.setCurrentDevices(currentDevices);
            response.setResult(success ? 0 : 1);

            logger.info("SetSourceDeviceRequest processed, result: {}", success ? "success" : "failed");

        } catch (Exception e) {
            logger.error("Error processing SetSourceDeviceRequest for service: {}", request.getServiceId(), e);
            success = false;
            response.setResult(1); // 失败

            // 返回空的设备列表
            SetSourceDeviceResponse.CurrentDevices emptyDevices = new SetSourceDeviceResponse.CurrentDevices();
            response.setCurrentDevices(emptyDevices);
        }

        return response;
    }

    /**
     * 检查服务是否正在运行
     * @param serviceId 服务ID
     * @return 如果服务正在运行返回true，否则返回false
     */
    private boolean isServiceRunning(String serviceId) {
        try {
            // 使用现有的服务状态查询逻辑
            ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
            boolean running = configXmlOperator.getIsRun(serviceId);

            logger.debug("Service {} , running: {}", serviceId, running);
            return running;

        } catch (Exception e) {
            logger.error("Error checking service status for service: {}", serviceId, e);
            // 出现异常时，为了安全起见，假设服务正在运行，不允许修改
            return true;
        }
    }

    private QueryRouteListResponse processQueryRouteListRequest(QueryRouteListRequest request) {
        logger.info("Processing QueryRouteListRequest for interface: {}", request.getInterfaceType());
        return new QueryRouteListLogic().queryRouteList(request);
    }

    private QueryCurRouteResponse processQueryCurRouteRequest(QueryCurRouteRequest request) {
        logger.info("Processing QueryCurRouteRequest for interface: {}", request.getInterfaceType());
        return new QueryCurRouteLogic().queryCurRoute(request);
    }

    private SetRouteListResponse processSetRouteListRequest(SetRouteListRequest request) {
        logger.info("Processing SetRouteListRequest for interface: {}", request.getInterfaceType());
        return new SetRouteListLogic().setRouteList(request);
    }

    private void sendHttpErrorResponse(HttpServletResponse response, int errorCode, String errorMessage) {
        try {
            // 如果是401未授权错误，直接返回纯文本响应
            if (errorCode == HttpServletResponse.SC_UNAUTHORIZED) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("text/plain;charset=UTF-8");
                response.getWriter().write("授权已过期，请联系管理员");
                return;
            }

            // 其他错误返回ASN.1格式的错误响应
            response.setContentType("application/octet-stream");

            MessageResponseFrame responseFrame = new MessageResponseFrame();
            responseFrame.setVersion(new Uint8(1));
            responseFrame.setContent(new MessageResponseFrame.Content());

            // 创建错误响应对象
            ErrorResponse errorResponse = new ErrorResponse();
            errorResponse.setErrorState(ProcessErrorState.messageStructureError);
            errorResponse.setMessageType(ContentMessageType.queryServiceStatus);
            // 设置错误响应
            responseFrame.getContent().setError(errorResponse);

            byte[] responseData = OssServAdapter.encode(responseFrame);
            response.setContentLength(responseData.length);
            try (OutputStream os = response.getOutputStream()) {
                os.write(responseData);
                os.flush();
            }

        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
        }
    }



}