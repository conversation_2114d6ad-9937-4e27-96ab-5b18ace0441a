package com.unimas.asn.http;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.unimas.asn.util.ConfigXmlOperator;
import com.unimas.asn.util.Constant;
import com.unimas.asn.util.DeviceConfigReader;
import com.unimas.asn.util.IPUtil;
import com.unimas.asn.util.SystemConfigManager;

/**
 * 请求验证器类，用于验证不同类型的请求
 */
public class RequestValidator {
    
    /**
     * 验证请求帧的基本结构
     * @param frame 请求帧
     * @throws ValidationException 如果验证失败
     */
    public static void validateRequestFrame(MessageRequestFrame frame) throws ValidationException {
        if (frame == null||frame.getContent() == null||frame.getVersion() == null) {
            throw new ValidationException("请求帧不能为空", ProcessErrorState.messageStructureError,null);
        }
    }
    
    /**
     * 验证服务配置请求
     * @param request 服务配置请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceConfigRequest(ServiceConfigRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务配置请求不能为空", ProcessErrorState.messageStructureError,null);
        }
        
        // 根据ServiceConfigRequest的CHOICE结构验证
        try {
            if (request.hasAddServiceRequest()) {
                validateAddServiceRequest(request.getAddServiceRequest());
            } else if (request.hasUpdateServiceRequest()) {
                validateUpdateServiceRequest(request.getUpdateServiceRequest());
            } else if (request.hasDeleteServiceRequest()) {
                validateDeleteServiceRequest(request.getDeleteServiceRequest());
            } else {
                throw new ValidationException("无效的服务配置请求类型", ProcessErrorState.messageStructureError,null);
            }
        } catch (Exception e) {
            if (e instanceof ValidationException) {
                throw (ValidationException) e;
            }
            throw new ValidationException("服务配置请求验证异常: " + e.getMessage(), ProcessErrorState.messageStructureError,null);
        }
    }
    
    /**
     * 验证添加服务请求
     */
    private static void validateAddServiceRequest(AddServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("添加服务请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.addService);
        }

        
        if (request.getDisplayname() == null || new String(request.getDisplayname().byteArrayValue()).isEmpty()) {
            throw new ValidationException("显示名称不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }

        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.addService);
        }
        if(request.getNetwork() == Network.sender){
            // 验证监听地址是否在设备上存在
            validateListeningAddress(request.getNetwork(), request.getProxyIp(), request.getServerIp(), ContentMessageType.addService);
        }
    }
    
    /**
     * 验证更新服务请求
     */
    private static void validateUpdateServiceRequest(UpdateServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("更新服务请求不能为空", ProcessErrorState.messageStructureError,ContentMessageType.updateService);
        }
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.updateService);
        }
        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.updateService);
        }
        if(request.getNetwork() == Network.sender){
            // 验证监听地址是否在设备上存在
            validateListeningAddress(request.getNetwork(), request.getProxyIp(), request.getServerIp(), ContentMessageType.updateService);
        }
    }
    
    /**
     * 验证删除服务请求
     */
    private static void validateDeleteServiceRequest(DeleteServiceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("删除服务请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.deleteService);
        }

        
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.deleteService);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.deleteService);
        }
    }
    
    /**
     * 验证服务控制请求
     * @param request 服务控制请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceControlRequest(ServiceControlRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务控制请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.controlService);
        }

        try {
            if (request.getServiceId() == null) {
                throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
            if(!isServiceExist(request.getServiceId().intValue()+"")){
                throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.controlService);
            }
            if (request.getServiceStartOrStop() == null) {
                throw new ValidationException("服务启停状态不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
        } catch (Exception e) {
            if(e instanceof ValidationException){
                throw e;
            }else{
                throw new ValidationException("服务控制请求验证异常: " + e.getMessage(),ProcessErrorState.illegalArgumentError,ContentMessageType.controlService);
            }
        }
    }
    
    /**
     * 验证服务状态查询请求
     * @param request 服务状态查询请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceStatusQueryRequest(ServiceStatusQueryRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务状态查询请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryServiceStatus);
        }
        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceStatus);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.queryServiceStatus);
        }

    }
    
    /**
     * 验证服务配置查询请求
     * @param request 服务配置查询请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateServiceConfigQueryRequest(ServiceConfigQueryRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("服务配置查询请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryServiceConfig);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }
        if(!isServiceExist(request.getServiceId().intValue()+"")){
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError,ContentMessageType.queryServiceConfig);
        }
        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }
        if(!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())){
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError,ContentMessageType.queryServiceConfig);
        }

    }
    
    /**
     * 验证报警报告请求
     * @param request 报警报告请求
     * @throws ValidationException 如果验证失败
     */
//    public static void validateAlarmReportRequest(AlarmReportRequest request) throws ValidationException {
//        if (request == null) {
//            throw new ValidationException("报警报告请求不能为空", 400);
//        }
//
//        try {
//            if (request.getServiceId() == null) {
//                throw new ValidationException("服务ID不能为空", 400);
//            }
//
//            if (request.getAlarmType() == null) {
//                throw new ValidationException("报警类型不能为空", 400);
//            }
//
//            if (request.getAlarmCode() == null) {
//                throw new ValidationException("报警代码不能为空", 400);
//            }
//
//            if (request.getAlarmStatus() == null) {
//                throw new ValidationException("报警状态不能为空", 400);
//            }
//        } catch (Exception e) {
//            throw new ValidationException("报警报告请求验证异常: " + e.getMessage(), 400);
//        }
//    }
    
    /**
     * 验证工作状态请求
     * @param request 工作状态请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateWorkStatusRequest(WorkStatusRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("工作状态请求不能为空",ProcessErrorState.messageStructureError,ContentMessageType.queryWorkStatus);
        }


        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空",ProcessErrorState.illegalArgumentError,ContentMessageType.queryWorkStatus);
        }

    }

    /**
     * 验证发送包统计请求
     * @param request 发送包统计请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSendPacketStatsRequest(SendPacketStatsRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("发送包统计请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.sendPacketStats);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        // 验证网络配置是否与当前设备配置匹配
        if (!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())) {
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
        }

        // 如果有period参数，验证其有效性
        if (request.hasPeriod()) {
            if (request.getPeriod().intValue() <= 0) {
                throw new ValidationException("时间段参数（分钟数）必须大于0", ProcessErrorState.illegalArgumentError, ContentMessageType.sendPacketStats);
            }
        }
    }

    /**
     * 验证接收包统计请求
     * @param request 接收包统计请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateReceivePacketStatsRequest(ReceivePacketStatsRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("接收包统计请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.receivePacketStats);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.receivePacketStats);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.receivePacketStats);
        }

        if (request.getNetwork() == null) {
            throw new ValidationException("网络配置不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.receivePacketStats);
        }

        // 验证网络配置是否与当前设备配置匹配
        if (!request.getNetwork().equals(DeviceConfigReader.getInstance().getNetwork())) {
            throw new ValidationException("网段参数错误", ProcessErrorState.illegalArgumentError, ContentMessageType.receivePacketStats);
        }

        // 如果有period参数，验证其有效性
        if (request.hasPeriod()) {
            if (request.getPeriod().intValue() <= 0) {
                throw new ValidationException("时间段参数（分钟数）必须大于0", ProcessErrorState.illegalArgumentError, ContentMessageType.receivePacketStats);
            }
        }
    }


    /**
     * 验证设置网口IP请求
     * @param request 设置网口IP请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSetInterfaceIpRequest(SetInterfaceIpRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("设置网口IP请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.setInterfaceIpService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        if (request.getInterfaceType() == null) {
            throw new ValidationException("网口类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        if (request.getIpAddress() == null) {
            throw new ValidationException("IP地址不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        if (request.getSubnetMask() == null) {
            throw new ValidationException("子网掩码不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        // 验证IP地址格式
        try {
            String ipStr = IPUtil.ipAddressToString(request.getIpAddress());
            if (ipStr == null || ipStr.isEmpty()) {
                throw new ValidationException("IP地址格式无效", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
            }
        } catch (Exception e) {
            throw new ValidationException("IP地址格式无效", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        // 验证子网掩码格式
        try {
            String maskStr = IPUtil.ipAddressToString(request.getSubnetMask());
            if (maskStr == null || maskStr.isEmpty()) {
                throw new ValidationException("子网掩码格式无效", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
            }
        } catch (Exception e) {
            throw new ValidationException("子网掩码格式无效", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
        }

        // 验证网关格式和可达性（如果提供）
        if (request.hasGateway()) {
            try {
                String gatewayStr = IPUtil.ipAddressToString(request.getGateway());
                if (gatewayStr == null || gatewayStr.isEmpty()) {
                    throw new ValidationException("网关地址格式无效", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
                }

//                // 验证网关可达性和网络段
//                String ipStr = IPUtil.ipAddressToString(request.getIpAddress());
//                String maskStr = IPUtil.ipAddressToString(request.getSubnetMask());
//                String interfaceName = (request.getInterfaceType() == InterfaceType.management) ? "eth0" : "eth1";
//
//                SystemConfigManager.GatewayValidationResult validationResult =
//                    SystemConfigManager.validateGateway(gatewayStr, ipStr, maskStr, interfaceName);
//
//                if (!validationResult.isValid()) {
//                    throw new ValidationException(validationResult.getMessage(), ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
//                }

            } catch (ValidationException e) {
                throw e; // 重新抛出验证异常
            } catch (Exception e) {
                throw new ValidationException("网关地址验证失败", ProcessErrorState.illegalArgumentError, ContentMessageType.setInterfaceIpService);
            }
        }
    }

    /**
     * 验证查询告警请求
     * @param request 查询告警请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateQueryAlarmRequest(QueryAlarmRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("查询告警请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.queryAlarm);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.queryAlarm);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.queryAlarm);
        }

        // 验证queryDay参数范围（1-31）
        long queryDay = request.getQueryDay();
        if (queryDay < 1 || queryDay > 31) {
            throw new ValidationException("查询日期必须在1-31范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.queryAlarm);
        }

        // 验证服务ID是否存在
        try {
            String serviceId = request.getServiceId().intValue() + "";
            if (!isServiceExist(serviceId)) {
                throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError, ContentMessageType.queryAlarm);
            }
        } catch (Exception e) {
            if (e instanceof ValidationException) {
                throw e;
            } else {
                throw new ValidationException("服务ID验证异常: " + e.getMessage(), ProcessErrorState.illegalArgumentError, ContentMessageType.queryAlarm);
            }
        }
    }

    /**
     * 验证查询上位机管理请求
     * @param request 查询上位机管理请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateQueryHostMngRequest(QueryHostMngRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("查询上位机管理请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.hostMngService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }
    }

    /**
     * 验证设置上位机管理请求
     * @param request 设置上位机管理请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSetHostMngRequest(SetHostMngRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("设置上位机管理请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.hostMngService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }

        // 验证端口范围（如果提供了端口）
        if (request.hasAuthPort() && (request.getAuthPort().intValue() < 1025 || request.getAuthPort().intValue() > 65535)) {
            throw new ValidationException("认证端口必须在1025-65535范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }

        if (request.hasAlarmPort() && (request.getAlarmPort().intValue() < 1025 || request.getAlarmPort().intValue() > 65535)) {
            throw new ValidationException("告警端口必须在1025-65535范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }

        if (request.hasCertMngPort() && (request.getCertMngPort().intValue() < 1025 || request.getCertMngPort().intValue() > 65535)) {
            throw new ValidationException("证书管理端口必须在1025-65535范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }

        if (request.hasSgPort() && (request.getSgPort().intValue() < 1025 || request.getSgPort().intValue() > 65535)) {
            throw new ValidationException("业务管理端口必须在1025-65535范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
        }
        if(request.hasCertMngPort() && request.hasSgPort()){
            if(request.getCertMngPort().intValue() != request.getSgPort().intValue()){
                throw new ValidationException("证书管理端口和业务管理端口不相同", ProcessErrorState.illegalArgumentError, ContentMessageType.hostMngService);
            }
        }
    }

    /**
     * 验证查询源设备请求
     * @param request 查询源设备请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateQuerySourceDeviceRequest(QuerySourceDeviceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("查询源设备请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.sourceDeviceService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
        }

        if (!isServiceExist(request.getServiceId().intValue() + "")) {
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError, ContentMessageType.sourceDeviceService);
        }
    }

    /**
     * 验证设置源设备请求
     * @param request 设置源设备请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSetSourceDeviceRequest(SetSourceDeviceRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("设置源设备请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.sourceDeviceService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
        }

        if (request.getServiceId() == null) {
            throw new ValidationException("服务ID不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
        }

        if (!isServiceExist(request.getServiceId().intValue() + "")) {
            throw new ValidationException("服务不存在", ProcessErrorState.serviceNotExistError, ContentMessageType.sourceDeviceService);
        }

        if (request.getSourcedevices() == null) {
            throw new ValidationException("源设备列表不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
        }

        // 验证源设备列表中的每个设备
        SetSourceDeviceRequest.Sourcedevices sourcedevices = request.getSourcedevices();
        for (int i = 0; i < sourcedevices.size(); i++) {
            SourceDevice device = sourcedevices.get(i);
            if (device.getIpAddress() == null) {
                throw new ValidationException("源设备IP地址不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
            }

            // 如果有端口，验证端口范围
            if (device.hasPort() && (device.getPort() < 1 || device.getPort() > 65535)) {
                throw new ValidationException("源设备端口必须在1-65535范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
            }
        }

        // 验证源设备地址是否有重复冲突
        validateSourceDeviceAddressDuplicates(sourcedevices);
    }

    /**
     * 验证查询路由列表请求
     * @param request 查询路由列表请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateQueryRouteListRequest(QueryRouteListRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("查询路由列表请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.iprouteService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (request.getInterfaceType() == null) {
            throw new ValidationException("网口类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        // 验证网口类型是否为有效值
        if (request.getInterfaceType() != InterfaceType.management &&
            request.getInterfaceType() != InterfaceType.business) {
            throw new ValidationException("无效的网口类型", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }
    }

    /**
     * 验证查询当前路由请求
     * @param request 查询当前路由请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateQueryCurRouteRequest(QueryCurRouteRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("查询当前路由请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.iprouteService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (request.getInterfaceType() == null) {
            throw new ValidationException("网口类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        // 验证网口类型是否为有效值
        if (request.getInterfaceType() != InterfaceType.management &&
            request.getInterfaceType() != InterfaceType.business) {
            throw new ValidationException("无效的网口类型", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }
    }

    /**
     * 验证设置路由列表请求
     * @param request 设置路由列表请求
     * @throws ValidationException 如果验证失败
     */
    public static void validateSetRouteListRequest(SetRouteListRequest request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("设置路由列表请求不能为空", ProcessErrorState.messageStructureError, ContentMessageType.iprouteService);
        }

        if (request.getMessageType() == null) {
            throw new ValidationException("消息类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (request.getInterfaceType() == null) {
            throw new ValidationException("网口类型不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        // 验证网口类型是否为有效值
        if (request.getInterfaceType() != InterfaceType.management &&
            request.getInterfaceType() != InterfaceType.business) {
            throw new ValidationException("无效的网口类型", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (request.getRouteList() == null) {
            throw new ValidationException("路由列表不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        // 验证路由列表中的每个路由
        SetRouteListRequest.RouteList routeList = request.getRouteList();
        for (int i = 0; i < routeList.size(); i++) {
            RouteList route = routeList.get(i);
            validateRouteEntry(route, i);
        }
    }

    /**
     * 验证单个路由条目
     * @param route 路由条目
     * @param index 路由索引
     * @throws ValidationException 如果验证失败
     */
    private static void validateRouteEntry(RouteList route, int index) throws ValidationException {
        if (route == null) {
            throw new ValidationException("路由条目 " + (index + 1) + " 不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (route.getDestination() == null) {
            throw new ValidationException("路由条目 " + (index + 1) + " 的目标网络地址不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (route.getSubnetMask() == null) {
            throw new ValidationException("路由条目 " + (index + 1) + " 的子网掩码不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }

        if (route.getGateway() == null) {
            throw new ValidationException("路由条目 " + (index + 1) + " 的网关地址不能为空", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }


        // 验证跃点数范围
        if (route.getMetric() < 0 || route.getMetric() > 9999) {
            throw new ValidationException("路由条目 " + (index + 1) + " 的跃点数必须在0-9999范围内", ProcessErrorState.illegalArgumentError, ContentMessageType.iprouteService);
        }
    }

    /**
     * 验证源设备地址是否有重复冲突
     * @param sourcedevices 源设备列表
     * @throws ValidationException 如果发现重复地址
     */
    private static void validateSourceDeviceAddressDuplicates(SetSourceDeviceRequest.Sourcedevices sourcedevices) throws ValidationException {
        if (sourcedevices == null || sourcedevices.size() <= 1) {
            return; // 空列表或单个设备无需检查重复
        }

        // 用于存储已见过的IP地址和IP:端口组合
        java.util.Set<String> seenIpAddresses = new java.util.HashSet<>();
        java.util.Set<String> seenIpPortCombinations = new java.util.HashSet<>();

        for (int i = 0; i < sourcedevices.size(); i++) {
            SourceDevice device = sourcedevices.get(i);
            if (device.getIpAddress() == null) {
                continue; // 跳过无效设备（这个在前面已经验证过了）
            }

            // 获取IP地址字符串
            String ipStr = IPUtil.ipAddressToString(device.getIpAddress());
            if (ipStr == null || ipStr.isEmpty()) {
                continue; // 跳过无效IP
            }

            // 检查IP地址是否重复
            if (seenIpAddresses.contains(ipStr)) {
                throw new ValidationException("源设备列表中存在重复的IP地址: " + ipStr,
                    ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
            }
            seenIpAddresses.add(ipStr);

            // 如果设备有端口，检查IP:端口组合是否重复
            if (device.hasPort()) {
                String ipPortCombination = ipStr + ":" + device.getPort();
                if (seenIpPortCombinations.contains(ipPortCombination)) {
                    throw new ValidationException("源设备列表中存在重复的IP:端口组合: " + ipPortCombination,
                        ProcessErrorState.illegalArgumentError, ContentMessageType.sourceDeviceService);
                }
                seenIpPortCombinations.add(ipPortCombination);
            }
        }
    }

    /**
     * 检查是否包含特殊字符（可用于防止注入攻击）
     * @param input 输入字符串
     * @return 是否包含特殊字符
     */
    public static boolean containsSpecialChars(String input) {
        if (input == null) {
            return false;
        }
        return input.matches(".*[;\\\\'\\\"/\\[\\]<>].*");
    }
    /**
     * 验证监听地址是否在设备上存在
     * @param network 网络类型（发送端或接收端）
     * @param proxyIp 代理IP（发送端使用）
     * @param serverIp 服务器IP（接收端使用）
     * @param messageType 消息类型（用于错误响应）
     * @throws ValidationException 如果监听地址不存在
     */
    private static void validateListeningAddress(Network network, IPAddress proxyIp, IPAddress serverIp, ContentMessageType messageType) throws ValidationException {
        String listeningAddress = null;

        // 根据网络类型确定监听地址
        if (network == Network.sender && proxyIp != null) {
            // 发送端使用proxyIp作为监听地址
            listeningAddress = IPUtil.ipAddressToString(proxyIp);
        } else if (network == Network.receiver && serverIp != null) {
            // 接收端使用serverIp作为监听地址
            listeningAddress = IPUtil.ipAddressToString(serverIp);
        }

        // 如果有监听地址，验证其是否在设备上存在
        if (listeningAddress != null && !listeningAddress.isEmpty()) {
            if (!isAddressExistOnDevice(listeningAddress)) {
                throw new ValidationException("配置的监听地址 " + listeningAddress + " 在设备上不存在（不是eth0/eth1设置的地址）",
                        ProcessErrorState.illegalArgumentError, messageType);
            }
        }
    }

    /**
     * 检查IP地址是否在设备的网卡上存在
     * @param ipAddress IP地址字符串
     * @return 如果地址存在返回true，否则返回false
     */
    private static boolean isAddressExistOnDevice(String ipAddress) {
        try {
            // 检查eth0网卡
            if (isAddressOnInterface(ipAddress, "eth0")) {
                return true;
            }

            // 检查eth1网卡
            if (isAddressOnInterface(ipAddress, "eth1")) {
                return true;
            }

            return false;
        } catch (Exception e) {
            // 如果检查过程中出现异常，记录日志但不抛出异常，返回false
            System.err.println("Error checking IP address on device: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查指定IP地址是否在指定网卡上
     * @param ipAddress IP地址
     * @param interfaceName 网卡名称
     * @return 如果地址在网卡上返回true，否则返回false
     */
    private static boolean isAddressOnInterface(String ipAddress, String interfaceName) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ip", "addr", "show", interfaceName);
            Process process = pb.start();

            try (java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("inet ") && !line.contains("inet6")) {
                        // 解析IP地址，格式如：inet *************/24 brd ************* scope global eth1
                        String[] parts = line.trim().split("\\s+");
                        for (int i = 0; i < parts.length; i++) {
                            if ("inet".equals(parts[i]) && i + 1 < parts.length) {
                                String ipWithPrefix = parts[i + 1];
                                String ip = ipWithPrefix.contains("/") ? ipWithPrefix.split("/")[0] : ipWithPrefix;
                                if (ipAddress.equals(ip)) {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }

            process.waitFor();
        } catch (Exception e) {
            System.err.println("Error checking IP address on interface " + interfaceName + ": " + e.getMessage());
        }

        return false;
    }

    private static boolean isServiceExist(String serviceId) {
        ConfigXmlOperator configXmlOperator = new ConfigXmlOperator(Constant.CONFIG_PATH);
        return configXmlOperator.isServiceExist(serviceId);
    }
}