package com.unimas.asn.db;

import com.unimas.asn.servicemanager.servicemanagementhttp.*;
import com.oss.asn1.IA5String;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for udp_alarm table
 */
public class AlarmDAO {
    private static final Logger logger = LoggerFactory.getLogger(AlarmDAO.class);



    private static final String SELECT_ALL_ALARMS =
            "SELECT id, appname, time, alarmtype, size, port, ip, message, status, syslog_status " +
            "FROM udp_alarm WHERE time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) ORDER BY time DESC";

    private static final String SELECT_ALL_ALARM_MESSAGES =
            "SELECT id, apptype, appname, time, alarmtype, message, status, count, send_status, syslog_status " +
            "FROM alarm_message WHERE time >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) and appname = '0' ORDER BY time DESC";

    private static final String SELECT_ALARMS_BY_SERVICE_AND_DAY =
            "SELECT id, appname, time, alarmtype, size, port, ip, message, status, syslog_status " +
            "FROM udp_alarm WHERE appname = ? AND DAY(time) = ? AND MONTH(time) = MONTH(CURDATE()) AND YEAR(time) = YEAR(CURDATE()) ORDER BY time DESC";

    private static final String SELECT_ALARM_MESSAGES_BY_SERVICE_AND_DAY =
            "SELECT id, apptype, appname, time, alarmtype, message, status, count, send_status, syslog_status " +
            "FROM alarm_message WHERE appname = ? AND DAY(time) = ? AND MONTH(time) = MONTH(CURDATE()) AND YEAR(time) = YEAR(CURDATE()) ORDER BY time DESC";

    private static final String INSERT_ALARM =
            "INSERT INTO udp_alarm (appname, time, alarmtype, size, port, ip, message, status, syslog_status) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_ALARM_STATUS =
            "UPDATE udp_alarm SET status = ? WHERE id = ?";

    private static final String UPDATE_SYSLOG_STATUS =
            "UPDATE udp_alarm SET syslog_status = 1 WHERE id = ?";

    private static final String UPDATE_ALARM_MESSAGE_SYSLOG_STATUS =
            "UPDATE alarm_message SET syslog_status = 1 WHERE id = ?";

    private static final String INSERT_ALARM_MESSAGE =
            "INSERT INTO alarm_message (apptype, appname, time, alarmtype, message, status, count, send_status, syslog_status) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

    /**
     * Fetches alarms from the database within the last 1 minute
     * @return List of AlarmReportRequest objects
     */
    public List<AlarmReportRequest> getAllAlarms() {
        List<AlarmReportRequest> alarms = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_ALL_ALARMS);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                AlarmReportRequest alarm = mapResultSetToAlarm(rs);
                alarms.add(alarm);
            }
        } catch (SQLException e) {
            logger.error("Error fetching alarms from database", e);
        }

        return alarms;
    }

    /**
     * Fetches alarms from the alarm_message table within the last 1 minute
     * @return List of AlarmReportRequest objects
     */
    public List<AlarmReportRequest> getAllAlarmMessages() {
        List<AlarmReportRequest> alarms = new ArrayList<>();

        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_ALL_ALARM_MESSAGES);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                AlarmReportRequest alarm = mapAlarmMessageResultSetToAlarm(rs);
                alarms.add(alarm);
            }
        } catch (SQLException e) {
            logger.error("Error fetching alarm messages from database", e);
        }

        return alarms;
    }

    /**
     * Inserts a new alarm into the alarm_message table for authentication failures
     * @param appType The application type
     * @param appName The application name
     * @param alarmType The alarm type code
     * @param message The alarm message
     * @return The ID of the inserted alarm, or -1 if insertion failed
     */
    public long insertAuthAlarm(String appType, String appName, int alarmType, String message) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_ALARM_MESSAGE, Statement.RETURN_GENERATED_KEYS)) {

            stmt.setString(1, appType);
            stmt.setString(2, appName);
            stmt.setTimestamp(3, new Timestamp(System.currentTimeMillis()));
            stmt.setInt(4, alarmType);
            stmt.setString(5, message);
            stmt.setInt(6, 0); // Default status
            stmt.setInt(7, 1); // Initial count
            stmt.setInt(8, 0); // Default send_status
            stmt.setInt(9, 0); // Default syslog_status

            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                return -1;
            }

            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    return generatedKeys.getLong(1);
                } else {
                    return -1;
                }
            }
        } catch (SQLException e) {
            logger.error("Error inserting authentication alarm into database", e);
            return -1;
        }
    }

    /**
     * Updates the status of an alarm
     * @param id The ID of the alarm
     * @param status The new status
     * @return true if successful, false otherwise
     */
    public boolean updateAlarmStatus(long id, AlarmStatus status) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_ALARM_STATUS)) {

            stmt.setInt(1, AlarmStatus.alarmRaised == status ? 1 : 0);
            stmt.setLong(2, id);

            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            logger.error("Error updating alarm status", e);
            return false;
        }
    }

    /**
     * Updates the syslog_status of an alarm to 1 (reported)
     * @param id The ID of the alarm
     * @return true if successful, false otherwise
     */
    public boolean updateSyslogStatus(long id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_SYSLOG_STATUS)) {

            stmt.setLong(1, id);

            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            logger.error("Error updating syslog status", e);
            return false;
        }
    }

    /**
     * Updates the syslog_status of multiple alarms to 1 (reported)
     * @param ids List of alarm IDs
     * @return number of successfully updated alarms
     */
    public int updateSyslogStatusBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        Connection conn = null;

        try {
            conn = DatabaseManager.getConnection();
            conn.setAutoCommit(false);

            try (PreparedStatement stmt = conn.prepareStatement(UPDATE_SYSLOG_STATUS)) {
                for (Long id : ids) {
                    stmt.setLong(1, id);
                    stmt.addBatch();
                }

                int[] results = stmt.executeBatch();

                for (int result : results) {
                    if (result > 0) {
                        successCount++;
                    }
                }

                conn.commit();
            }
        } catch (SQLException e) {
            logger.error("Error updating syslog status batch", e);
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    logger.error("Error rolling back transaction", ex);
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    logger.error("Error closing connection", e);
                }
            }
        }

        return successCount;
    }

    /**
     * Updates the syslog_status of an alarm message to 1 (reported)
     * @param id The ID of the alarm message
     * @return true if successful, false otherwise
     */
    public boolean updateAlarmMessageSyslogStatus(long id) {
        try (Connection conn = DatabaseManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_ALARM_MESSAGE_SYSLOG_STATUS)) {

            stmt.setLong(1, id);

            int affectedRows = stmt.executeUpdate();
            return affectedRows > 0;
        } catch (SQLException e) {
            logger.error("Error updating alarm message syslog status", e);
            return false;
        }
    }

    /**
     * Updates the syslog_status of multiple alarm messages to 1 (reported)
     * @param ids List of alarm message IDs
     * @return number of successfully updated alarm messages
     */
    public int updateAlarmMessageSyslogStatusBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        Connection conn = null;

        try {
            conn = DatabaseManager.getConnection();
            conn.setAutoCommit(false);

            try (PreparedStatement stmt = conn.prepareStatement(UPDATE_ALARM_MESSAGE_SYSLOG_STATUS)) {
                for (Long id : ids) {
                    stmt.setLong(1, id);
                    stmt.addBatch();
                }

                int[] results = stmt.executeBatch();

                for (int result : results) {
                    if (result > 0) {
                        successCount++;
                    }
                }

                conn.commit();
            }
        } catch (SQLException e) {
            logger.error("Error updating alarm message syslog status batch", e);
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    logger.error("Error rolling back transaction", ex);
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.setAutoCommit(true);
                    conn.close();
                } catch (SQLException e) {
                    logger.error("Error closing connection", e);
                }
            }
        }

        return successCount;
    }

    /**
     * Maps a database result set row to an AlarmReportRequest object
     */
    private AlarmReportRequest mapResultSetToAlarm(ResultSet rs) throws SQLException {
        AlarmReportRequest alarm = new AlarmReportRequest();

        // Set message type for alarm report
        alarm.setMessageType(ContentMessageType.reportAlarm);

        // Set service ID
        alarm.setServiceId(new ServiceId(Short.parseShort(rs.getString("appname"))));

        // Create AlarmItem from database data
        AlarmItem alarmItem = new AlarmItem();

        // Map alarm type and code
        int alarmTypeId = rs.getInt("alarmtype");
        AlarmCode alarmCode = udpCodeMap2ASN(alarmTypeId);
        alarmItem.setAlarmCode(alarmCode);
        alarmItem.setAlarmType(code2AlarmType(alarmCode));

        // Set alarm description
        alarmItem.setAlarmDesc(new IA5String(rs.getString("message")));

        // Set alarm count (default to 1 for individual alarms)
        alarmItem.setAlarmCount(new Uint64(new BigInteger("1")));

        // Create alarms list and add the alarm item
        AlarmReportRequest.Alarms alarms = new AlarmReportRequest.Alarms();
        alarms.add(alarmItem);
        alarm.setAlarms(alarms);

        return alarm;
    }

    /**
     * Maps a database result set row from alarm_message table to an AlarmReportRequest object
     */
    private AlarmReportRequest mapAlarmMessageResultSetToAlarm(ResultSet rs) throws SQLException {
        AlarmReportRequest alarm = new AlarmReportRequest();

        // Set message type for alarm report
        alarm.setMessageType(ContentMessageType.reportAlarm);

        String appname = rs.getString("appname");

        // Parse appname as service ID, fallback to default if not parseable
        try {
            alarm.setServiceId(new ServiceId(Short.parseShort(appname)));
        } catch (NumberFormatException e) {
            // Default to 0 if appname is not a number
            alarm.setServiceId(new ServiceId((short)0));
            logger.warn("Invalid service ID format in alarm_message: {}", appname);
        }

        // Create AlarmItem from database data
        AlarmItem alarmItem = new AlarmItem();

        // Map alarm type and code
        int alarmTypeId = rs.getInt("alarmtype");
        AlarmCode alarmCode = udpCodeMap2ASN(alarmTypeId);
        alarmItem.setAlarmCode(alarmCode);
        alarmItem.setAlarmType(code2AlarmType(alarmCode));

        // Set alarm description
        String message = rs.getString("message");
        alarmItem.setAlarmDesc(new IA5String(message));

        // Set alarm count from database count field
        int count = rs.getInt("count");
        alarmItem.setAlarmCount(new Uint64(new BigInteger(String.valueOf(count))));

        // Create alarms list and add the alarm item
        AlarmReportRequest.Alarms alarms = new AlarmReportRequest.Alarms();
        alarms.add(alarmItem);
        alarm.setAlarms(alarms);

        return alarm;
    }

    private AlarmType code2AlarmType(AlarmCode code) {
        AlarmType alarmType = null;
        if (code == AlarmCode.illegalCertificate || code == AlarmCode.protocolVerifyFailure || code == AlarmCode.keywordCheckFailure) {
            alarmType = AlarmType.securityAlarm;
        } else {
            alarmType = AlarmType.faultAlarm;
        }
        return alarmType;
    }

    private AlarmCode udpCodeMap2ASN(int code) {
        AlarmCode ret = null;
        switch (code) {
            case 1:
            case 5:
            case 6:
                ret = AlarmCode.keywordCheckFailure;
                break;
            case 2:
                ret = AlarmCode.channelException;
                break;
            case 3:
            case 4:
                ret = AlarmCode.protocolVerifyFailure;
                break;
            case 7:
                ret = AlarmCode.illegalCertificate;
                break;
            default:
                ret = AlarmCode.deviceException;
                break;

        }
        return ret;
    }
}
