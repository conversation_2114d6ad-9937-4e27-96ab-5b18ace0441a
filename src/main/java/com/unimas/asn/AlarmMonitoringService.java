package com.unimas.asn;

import com.unimas.asn.db.AlarmDAO;
import com.unimas.asn.db.DatabaseManager;
import com.unimas.asn.client.AlarmReportClient;
import com.unimas.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Service that periodically checks for alarms in the database and sends reports
 */
public class AlarmMonitoringService {
    private static final Logger logger = LoggerFactory.getLogger(AlarmMonitoringService.class);
    private static final int DEFAULT_QUERY_INTERVAL_SECONDS = 60;
    private static final int CONFIG_CHECK_INTERVAL_SECONDS = 5; // 每5秒检查一次配置文件修改

    private final AlarmDAO alarmDAO;
    private final AlarmReportClient reportClient;
    private final ScheduledExecutorService scheduler;
    private final int queryIntervalSeconds;
    private boolean previousEnabledState = true; // Track the previous state to detect changes

    /**
     * Creates a new AlarmMonitoringService with default interval of 10 seconds
     */
    public AlarmMonitoringService() {
        this(DEFAULT_QUERY_INTERVAL_SECONDS);
    }

    /**
     * Creates a new AlarmMonitoringService with specified interval
     * @param queryIntervalSeconds interval in seconds between database queries
     */
    public AlarmMonitoringService(int queryIntervalSeconds) {
        this.alarmDAO = new AlarmDAO();
        this.reportClient = new AlarmReportClient();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.queryIntervalSeconds = queryIntervalSeconds;
        this.previousEnabledState = reportClient.isEnabled();

        logger.info("Initializing alarm monitoring service with reporting enabled={}", previousEnabledState);
    }

    /**
     * Starts the monitoring service
     */
    public void start() {
        logger.info("Starting alarm monitoring service with {} second interval", queryIntervalSeconds);

        // Schedule the main alarm checking task
        scheduler.scheduleAtFixedRate(this::checkAndReportAlarms, 0, queryIntervalSeconds, TimeUnit.SECONDS);

        // 定时检查配置文件变更
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 检查配置文件是否被修改
                boolean reloaded = reportClient.checkAndReloadConfigIfNeeded();
                boolean currentEnabledState = reportClient.isEnabled();

                // 如果配置被重新加载或启用状态发生变化
                if (reloaded || previousEnabledState != currentEnabledState) {
                    logger.info("报警上报配置已更新. 目标地址: {}:{}, 启用状态: {}",
                            reportClient.getDestinationIp(),
                            reportClient.getDestinationPort(),
                            currentEnabledState);

                    // 如果从禁用变为启用
                    if (!previousEnabledState && currentEnabledState) {
                        logger.info("报警上报功能已启用，恢复数据库查询");
                    }

                    // 如果从启用变为禁用
                    if (previousEnabledState && !currentEnabledState) {
                        logger.info("报警上报功能已禁用，暂停数据库查询");
                    }

                    // 更新状态记录
                    previousEnabledState = currentEnabledState;
                }
            } catch (Exception e) {
                logger.error("检查配置文件时发生错误", e);
            }
        }, CONFIG_CHECK_INTERVAL_SECONDS, CONFIG_CHECK_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }

    /**
     * Stops the monitoring service
     */
    public void stop() {
        logger.info("Stopping alarm monitoring service");

        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // Close database connections
        DatabaseManager.closeDataSource();
    }

    /**
     * Queries database for alarms and sends reports
     * This is the main method that runs on the scheduled thread
     */
    private void checkAndReportAlarms() {
        try {
            // Check if alarm reporting is enabled before querying the database
            if (!reportClient.isEnabled()) {
                logger.debug("Alarm reporting is disabled, skipping database queries");
                return;
            }

            // Process alarms from udp_alarm table
            processUdpAlarms();

            // Process alarms from alarm_message table
            processAlarmMessages();
        } catch (Exception e) {
            logger.error("Error in alarm monitoring thread", e);
        }
    }

    /**
     * Processes alarms from the udp_alarm table
     */
    private void processUdpAlarms() {
        try {
            logger.debug("Querying udp_alarm table for alarms with syslog_status=0...");

            // Get all alarms from udp_alarm table with syslog_status=0
            List<AlarmReportRequest> alarms = alarmDAO.getAllAlarms();

            logger.info("Found {} alarms in udp_alarm table with syslog_status=0", alarms.size());

            if (alarms.isEmpty()) {
                return;
            }

            // Send each alarm report
            for (AlarmReportRequest alarm : alarms) {
                boolean sent = reportClient.sendAlarmReport(alarm);
                if (sent) {
                    logger.debug("Successfully sent alarm report for service: {}", alarm.getServiceId());
                } else {
                    logger.warn("Failed to send alarm report for service: {}", alarm.getServiceId());
                }
            }
        } catch (Exception e) {
            logger.error("Error processing udp_alarms", e);
        }
    }

    /**
     * Processes alarms from the alarm_message table
     */
    private void processAlarmMessages() {
        try {
            logger.debug("Querying alarm_message table for alarms with syslog_status=0...");

            // Get all alarm messages with syslog_status=0
            List<AlarmDAO.AlarmWithId> alarms = alarmDAO.getAllAlarmMessages();

            logger.info("Found {} alarms in alarm_message table with syslog_status=0", alarms.size());

            if (alarms.isEmpty()) {
                return;
            }

            // List to keep track of successfully reported alarm message IDs
            List<Long> reportedAlarmIds = new ArrayList<>();

            // Send each alarm report
            for (AlarmDAO.AlarmWithId alarmWithId : alarms) {
                AlarmReportRequest alarm = alarmWithId.getRequest();
                boolean sent = reportClient.sendAlarmReport(alarm);
                if (sent) {
                    logger.debug("Successfully sent alarm message report for service: {}", alarm.getServiceId());

                    // Get the ID from the wrapper
                    Long alarmId = alarmWithId.getId();
                    if (alarmId != null) {
                        reportedAlarmIds.add(alarmId);
                    } else {
                        logger.warn("Could not find ID for reported alarm message");
                    }
                } else {
                    logger.warn("Failed to send alarm message report for service: {}", alarm.getServiceId());
                }
            }

            // Update syslog_status for successfully reported alarm messages
            if (!reportedAlarmIds.isEmpty()) {
                int updatedCount = alarmDAO.updateAlarmMessageSyslogStatusBatch(reportedAlarmIds);
                logger.info("Updated syslog_status to 1 for {} out of {} reported alarms from alarm_message",
                        updatedCount, reportedAlarmIds.size());
            }
            // Clear the alarm tracker
//            AlarmTracker.clearAlarms();
        } catch (Exception e) {
            logger.error("Error processing alarm_messages", e);
        }
    }
}
