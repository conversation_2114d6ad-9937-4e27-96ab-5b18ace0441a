package com.unimas.asn;

import com.unimas.asn.db.AlarmDAO;
import com.unimas.asn.db.DatabaseManager;
import com.unimas.asn.client.AlarmReportClient;
import com.unimas.asn.servicemanager.servicemanagementhttp.AlarmReportRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Service that periodically checks for alarms in the database and sends reports
 */
public class AlarmMonitoringService {
    private static final Logger logger = LoggerFactory.getLogger(AlarmMonitoringService.class);
    private static final int DEFAULT_QUERY_INTERVAL_SECONDS = 60;
    private static final int CONFIG_CHECK_INTERVAL_SECONDS = 5; // 每5秒检查一次配置文件修改

    private final AlarmDAO alarmDAO;
    private final AlarmReportClient reportClient;
    private final ScheduledExecutorService scheduler;
    private final int queryIntervalSeconds;
    private boolean previousEnabledState = true; // Track the previous state to detect changes

    /**
     * Creates a new AlarmMonitoringService with default interval of 10 seconds
     */
    public AlarmMonitoringService() {
        this(DEFAULT_QUERY_INTERVAL_SECONDS);
    }

    /**
     * Creates a new AlarmMonitoringService with specified interval
     * @param queryIntervalSeconds interval in seconds between database queries
     */
    public AlarmMonitoringService(int queryIntervalSeconds) {
        this.alarmDAO = new AlarmDAO();
        this.reportClient = new AlarmReportClient();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.queryIntervalSeconds = queryIntervalSeconds;
        this.previousEnabledState = reportClient.isEnabled();

        logger.info("Initializing alarm monitoring service with reporting enabled={}", previousEnabledState);
    }

    /**
     * Starts the monitoring service
     */
    public void start() {
        logger.info("Starting alarm monitoring service with {} second interval", queryIntervalSeconds);

        // Schedule the main alarm checking task
        scheduler.scheduleAtFixedRate(this::checkAndReportAlarms, 0, queryIntervalSeconds, TimeUnit.SECONDS);

        // 定时检查配置文件变更
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 检查配置文件是否被修改
                boolean reloaded = reportClient.checkAndReloadConfigIfNeeded();
                boolean currentEnabledState = reportClient.isEnabled();

                // 如果配置被重新加载或启用状态发生变化
                if (reloaded || previousEnabledState != currentEnabledState) {
                    logger.info("报警上报配置已更新. 目标地址: {}:{}, 启用状态: {}",
                            reportClient.getDestinationIp(),
                            reportClient.getDestinationPort(),
                            currentEnabledState);

                    // 如果从禁用变为启用
                    if (!previousEnabledState && currentEnabledState) {
                        logger.info("报警上报功能已启用，恢复数据库查询");
                    }

                    // 如果从启用变为禁用
                    if (previousEnabledState && !currentEnabledState) {
                        logger.info("报警上报功能已禁用，暂停数据库查询");
                    }

                    // 更新状态记录
                    previousEnabledState = currentEnabledState;
                }
            } catch (Exception e) {
                logger.error("检查配置文件时发生错误", e);
            }
        }, CONFIG_CHECK_INTERVAL_SECONDS, CONFIG_CHECK_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }

    /**
     * Stops the monitoring service
     */
    public void stop() {
        logger.info("Stopping alarm monitoring service");

        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }

        // Close database connections
        DatabaseManager.closeDataSource();
    }

    /**
     * Queries database for alarms and sends reports
     * This is the main method that runs on the scheduled thread
     */
    private void checkAndReportAlarms() {
        try {
            // Check if alarm reporting is enabled before querying the database
            if (!reportClient.isEnabled()) {
                logger.debug("Alarm reporting is disabled, skipping database queries");
                return;
            }

            // Process all alarms from both tables
            processAllAlarms();
        } catch (Exception e) {
            logger.error("Error in alarm monitoring thread", e);
        }
    }

    /**
     * Processes alarms from both udp_alarm and alarm_message tables
     */
    private void processAllAlarms() {
        try {
            logger.debug("Querying both udp_alarm and alarm_message tables for alarms within the last 1 minute...");

            // Get all alarms from both tables within the last 1 minute
            List<AlarmReportRequest> allAlarms = new ArrayList<>();

            // Get alarms from udp_alarm table
            List<AlarmReportRequest> udpAlarms = alarmDAO.getAllAlarms();
            logger.info("Found {} alarms in udp_alarm table within the last 1 minute", udpAlarms.size());
            allAlarms.addAll(udpAlarms);

            // Get alarms from alarm_message table
            List<AlarmReportRequest> messageAlarms = alarmDAO.getAllAlarmMessages();
            logger.info("Found {} alarms in alarm_message table within the last 1 minute", messageAlarms.size());
            allAlarms.addAll(messageAlarms);

            logger.info("Total {} alarms to be reported", allAlarms.size());

            if (allAlarms.isEmpty()) {
                return;
            }

            // Send each alarm report individually (keeping separate requests for different service IDs)
            for (AlarmReportRequest alarm : allAlarms) {
                boolean sent = reportClient.sendAlarmReport(alarm);
                if (sent) {
                    logger.debug("Successfully sent alarm report for service: {}", alarm.getServiceId());
                } else {
                    logger.warn("Failed to send alarm report for service: {}", alarm.getServiceId());
                }
            }
        } catch (Exception e) {
            logger.error("Error processing alarms", e);
        }
    }
}
